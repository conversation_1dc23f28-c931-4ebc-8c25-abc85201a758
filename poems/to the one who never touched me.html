<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>to the one who never touched me | Sex & Love Letters</title>
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/to-the-one-who-never-touched-me.html">
    <meta property="og:title" content="to the one who never touched me | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/to-the-one-who-never-touched-me.html">
    <meta property="twitter:title" content="to the one who never touched me | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
        
        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .poem-content p {
            margin-bottom: 0.7em;
        }
        
        .letter-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .letter-content p {
            margin-bottom: 0.7em;
        }
        
        .story-content {
            white-space: pre-wrap;
            line-height: 1.7;
        }
        
        .story-content h2 {
            margin-top: 2em;
            margin-bottom: 1em;
            font-size: 1.5rem;
        }
        
        .story-content p {
            margin-bottom: 1em;
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">to the one who never touched me</h2>
            
            
            
            <div class="poem-content my-8">
                <p>to the one who never touched me</p><p>you never held me</p><p>not really</p><p>but i carry you</p><p>in the static between my legs</p><p>in the ache behind my ribs</p><p>in the dream sweat i wake to</p><p>mouth open</p><p>saying your name without sound</p><p>you never kissed me</p><p>not really</p><p>but your breath still fogs my mirror</p><p>when i whisper</p><p>the things i never had the courage to write</p><p>when i trace</p><p>the outline of what could have been</p><p>onto the backs of strangers</p><p>who ask me to stay the night</p><p>what were we</p><p>nothing</p><p>but fuck</p><p>didn't it burn</p><p>we never made love</p><p>but you were the first</p><p>i wanted to be ruined by</p><p>i folded every version of myself</p><p>into an origami offering</p><p>and placed it</p><p>on your altar of indifference</p><p>if i am a poem now</p><p>it is because of the silence</p><p>you left behind</p>
            </div>
            
            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=to%20the%20one%20who%20never%20touched%20me&url=https://sexandloveletters.com/poems/to-the-one-who-never-touched-me.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/to-the-one-who-never-touched-me.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>
            
            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="the small of your back.html" class="text-accent hover:underline">← the small of your back</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="what remains.html" class="text-accent hover:underline">what remains →</a>
            </div>
        </div>
    </main>

    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
</body>
</html>