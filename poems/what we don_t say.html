<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>what we don't say | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/what-we-don't-say.html">
    <meta property="og:title" content="what we don't say | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/what-we-don't-say.html">
    <meta property="twitter:title" content="what we don't say | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <link rel="stylesheet" href="../assets/styles.css">
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">what we don't say</h2>

            <div class="poem-content my-8">
                <p>what we don't say</p><p>you sit across from me</p><p>your legs folded like apology</p><p>your hands restless</p><p>like they’re looking for a place to land</p><p>i feel it in my throat</p><p>the way your sadness rises</p><p>and i swallow it</p><p>again</p><p>you tilt your head when you listen</p><p>like you’re trying to catch the version of me</p><p>that doesn't flinch</p><p>but she’s late</p><p>always late</p><p>i keep my spine straight</p><p>keep my face open</p><p>keep my breath slow</p><p>but my palms sweat</p><p>every time you say</p><p>“i don’t want to be here anymore”</p><p>like a secret you’ve made peace with</p><p>i want to touch your wrist</p><p>not to save you</p><p>but just to say</p><p>i see you</p><p>i feel it</p><p>right here</p><p>under the skin</p><p>where it lives like a tremor</p><p>i feel seen</p><p>and it makes me want to vanish</p><p>or be held</p><p>or fuck</p><p>i don’t know</p><p>something</p><p>anything</p><p>to make this sharpness quiet</p><p>you don’t move</p><p>you never move</p><p>but your pupils dilate</p><p>just enough</p><p>for me to know</p><p>you’re in it too</p><p>you smell like cedar and stillness</p><p>and something clean</p><p>like someone who has never been called too much</p><p>you carry your concern</p><p>in your jaw</p><p>you bite it back</p><p>and still</p><p>i feel it</p><p>every time</p><p>you look at me like that</p><p>when you leave</p><p>i sit in the chair you sat in</p><p>just for a second</p><p>to remember what it felt like</p><p>to hold that much fire</p><p>and not burn</p><p>when i leave</p><p>i check the mirror in the hallway</p><p>to see if i’m still here</p><p>to see if you erased me</p><p>or saved me</p><p>some days i forget</p><p>who is the one unraveling</p><p>and who is the one watching</p><p>but we are both</p><p>so</p><p>unbelievably</p><p>still</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=what%20we%20don't%20say&url=https://sexandloveletters.com/poems/what-we-don't-say.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/what-we-don't-say.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="what-remains.html" class="text-accent hover:underline">← what remains</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="when-i-could-not-speak.html" class="text-accent hover:underline">when i could not speak →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>