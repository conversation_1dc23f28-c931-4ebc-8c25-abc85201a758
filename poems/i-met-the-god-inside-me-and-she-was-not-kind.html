<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>i met the god inside me and she was not kind | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/i-met-the-god-inside-me-and-she-was-not-kind.html">
    <meta property="og:title" content="i met the god inside me and she was not kind | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/i-met-the-god-inside-me-and-she-was-not-kind.html">
    <meta property="twitter:title" content="i met the god inside me and she was not kind | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }

        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .poem-content p {
            margin-bottom: 0.7em;
        }

        
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute right-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">i met the god inside me and she was not kind</h2>

            <div class="poem-content my-8">
                <p>i met the god inside me and she was not kind</p><p>she did not whisper</p><p>she screamed</p><p>she threw my name back at me</p><p>like a curse</p><p>and laughed when i flinched</p><p>she was barefoot</p><p>bloody</p><p>beautiful in the way a collapsing star is beautiful</p><p>all gravity and no mercy</p><p>i asked her</p><p>why do you hurt me</p><p>she said</p><p>because you keep calling it love</p><p>she carried a knife</p><p>but it was not for others</p><p>it was for carving the truth</p><p>into my own ribs</p><p>word by word</p><p>name by name</p><p>lie by lie</p><p>she kissed me once</p><p>on the mouth</p><p>and i tasted rust and birth</p><p>she told me</p><p>you are not broken</p><p>you are feral</p><p>you are not lonely</p><p>you are wild</p><p>stop waiting</p><p>for someone</p><p>to come</p><p>and calm</p><p>your storms</p><p>you were never meant</p><p>to be calm</p><p>you are the sea</p><p>the howl</p><p>the unfinished prayer</p><p>so rise</p><p>dripping</p><p>half-mad</p><p>eyes full of fire</p><p>and teeth bared in grin</p><p>and love them</p><p>if you must</p><p>but never</p><p>ever again</p><p>forget who you are</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=i%20met%20the%20god%20inside%20me%20and%20she%20was%20not%20kind&url=https://sexandloveletters.com/poems/i-met-the-god-inside-me-and-she-was-not-kind.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/i-met-the-god-inside-me-and-she-was-not-kind.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="i-kiss-you.html" class="text-accent hover:underline">← i kiss you</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="im-a-leg-man.html" class="text-accent hover:underline">im a leg man →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>