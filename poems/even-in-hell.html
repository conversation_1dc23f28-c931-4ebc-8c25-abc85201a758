<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>even in hell | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/even-in-hell.html">
    <meta property="og:title" content="even in hell | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/even-in-hell.html">
    <meta property="twitter:title" content="even in hell | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }

        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .poem-content p {
            margin-bottom: 0.7em;
        }

        
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">even in hell</h2>

            <div class="poem-content my-8">
                <p>forget armageddon</p><p>it's in between hell and earth</p><p>where there is heaven</p><p>just a piece, sure</p><p>but it's the slice of life we endure</p><p>that makes the breaking and making</p><p>of bent backs worth the labour</p><p>that refuse to believe it was a favour</p><p>you made it, in spite of</p><p>despite your, lame arms and sore heart</p><p>don't blame yourself for kicking and screaming</p><p>you were trying hard to not quit believing</p><p>don't take it personally when they verse you</p><p>they were just trying to make believe too</p><p>it's not you.</p><p>it's us.</p><p>the muck, the muddled who make a fuss</p><p>about the things that don't make sense</p><p>about what does not matter</p><p>what sits in our heads</p><p>you believe </p><p>you see</p><p>you, me</p><p>we</p><p>i'm so tired i could die</p><p>so dire i try</p><p>so tried that i cry</p><p>and just lie</p><p>down </p><p>wishing it would all slow down</p><p>so i can fucking think before i mow down</p><p>slow down</p><p>i always thought that when i made it</p><p>i'd go balls deep into helping </p><p>so that it makes some difference</p><p>but we're all yelping</p><p>in some depressing animal shelter</p><p>all of us still dreaming</p><p>of making it</p><p>we made it</p><p>today</p><p>and tomorrow</p><p>and the day after</p><p>dreading the day we die</p><p>trying to get rich to buy</p><p>another day</p><p>just to make it</p><p>to heaven</p><p>hell is far worse</p><p>a curse?</p><p>superstition</p><p>first?</p><p>a position</p><p>thirst?</p><p>greed for living</p><p>nursed?</p><p>a paid wife</p><p>late?</p><p>just a date</p><p>a sick sigh</p><p>euphemisms for tired eyes</p><p>rewind a little</p><p>isn't this the gift of random-ness?</p><p>a twist of the sandman’s plot?</p><p>the remarkable angles and holes</p><p>the fucking kissing and loving </p><p>the joy of becoming</p><p>the noise of something</p><p>that makes the heart swell </p><p>like wooden sandals in water</p><p>the rising tides crushing </p><p>when you feel something so amazing</p><p>you howl like a wolf at the moon</p><p>and feel no bitterness</p><p>because when</p><p>you slow down</p><p>and shiver</p><p>from the fever</p><p>of being alive</p><p>you sigh</p><p>you made it another day</p><p>and will tomorrow</p><p>and the day after</p><p>and that's heaven</p><p>even, in hell</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=even%20in%20hell&url=https://sexandloveletters.com/poems/even-in-hell.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/even-in-hell.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="economically.html" class="text-accent hover:underline">← economically</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="finding-where-we-laughed.html" class="text-accent hover:underline">finding where we laughed →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>