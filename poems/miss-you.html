<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>miss you | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750050583" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750050583" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750050583">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750050583">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/miss-you.html">
    <meta property="og:title" content="miss you | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/miss-you.html">
    <meta property="twitter:title" content="miss you | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
        
        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .poem-content p {
            margin-bottom: 0.7em;
        }
        
        
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">miss you</h2>

            <div class="poem-content my-8">
                <p>hey baby,</p><p>just texting to say that was one hell of a kiss.</p><p>a wish, really. that you didn’t have to leave so soon.</p><p>i feel like a fool to swoon so fast, but i think you know this</p><p>was more than love-bombing, more than hope-rope climbing,</p><p>more than no nopes tonight-ing.</p><p>we were humming.</p><p>fingers crawling, dawdling, tightening.</p><p>lightning, blind and wanting.</p><p>so baby,</p><p>just checking in.</p><p>when will i see you again?</p><p>it’s been too long.</p><p>a hundred songs and still nothing.</p><p>i made you your favourite dish,</p><p>you know, the one with fish</p><p>and the aioli from where our date began,</p><p>that restaurant where you clawed at my pants,</p><p>made me look away and blush like a kid.</p><p>damn.</p><p>now i’m overdoing it.</p><p>it’s just,</p><p>i am so scared to blow this.</p><p>now i’m more crazy than owning it.</p><p>and it always has to be two ways of showing up.</p><p>all or nothing never enough.</p><p>i explode.</p><p>i show too much.</p><p>tremble when you moan like</p><p>it’s the end of the world</p><p>and i’m all you’ve got.</p><p>when you said</p><p>please don’t stop</p><p>and i couldn’t.</p><p>i still can’t…</p><p>thinking about you.</p><p>i bought you flowers.</p><p>they’re not doing so great now.</p><p>pale.</p><p>powerless.</p><p>like the makeup on your pillow,</p><p>soured yellow.</p><p>i think you blocked me.</p><p>and now i can’t unsee</p><p>what you may have seen</p><p>that made me unclean</p><p>enough to leave</p><p>without a hint.</p><p>oh.</p><p>it’s because i kept sending you poetry.</p><p>my bad.</p><p>that’s on me.</p><p>i still taste your cum in my mouth.</p><p>i treasure it like words that would never come out.</p><p>words that should never come out.</p><p>you do know what this is about, right?</p><p>i don’t explain my words.</p><p>that ruins the punch.</p><p>they hit harder when they rhyme,</p><p>when they carry the yards of yarn</p><p>i would walk just to belong</p><p>to something i hung </p><p>onto</p><p>when i met you</p><p>and got all romantic</p><p>and spent you</p><p>all night long.</p><p>we aimed for the moon</p><p>and missed</p><p>the idea of this.</p><p>the idea of this.</p><p>that is what words are.</p><p>ideas.</p><p>i never meant this.</p><p>i thought i belonged to you.</p><p>and now i am still</p><p>searching</p><p>for the sender</p><p>where i can return myself to</p><p>whatever it is i turned into</p><p>after i met you.</p><p>miss you.</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=miss%20you&url=https://sexandloveletters.com/poems/miss-you.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/miss-you.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="im-a-leg-man.html" class="text-accent hover:underline">← im a leg man</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="not-like-this.html" class="text-accent hover:underline">not like this →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>