<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>not like this | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/not-like-this.html">
    <meta property="og:title" content="not like this | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/not-like-this.html">
    <meta property="twitter:title" content="not like this | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }

        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .poem-content p {
            margin-bottom: 0.7em;
        }

        
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">not like this</h2>

            <div class="poem-content my-8">
                <p>where did respect leave my pen</p><p>when my lips paid you disservice</p><p>and i left with the curses</p><p>this cursor</p><p>blinking blackout</p><p>and with a wink i see the stars</p><p>my arms are empty</p><p>cheap words and a keyboard</p><p>a hero’s disapproval</p><p>write something of substance</p><p>don’t be the villain</p><p>i ached for my removal</p><p>and that set the motion going</p><p>spiralling in threes and fives and times</p><p>i didn’t say i’m done</p><p>i just said i’m trying</p><p>oh boy</p><p>there is a difference</p><p>not anymore</p><p>that’s not fib law</p><p>just a pattern</p><p>where everything seemed to happen</p><p>too soon</p><p>too fast</p><p>now i am a stranger</p><p>a danger to the flavour</p><p>that tastes secure and vanilla</p><p>here by the safety of the fucking tightrope</p><p>we all walk</p><p>some fall</p><p>should know better</p><p>than to be vulnerable</p><p>hounds with tongues</p><p>that lick hunger young</p><p>brutes with calculators and compasses</p><p>navigating through a single truth</p><p>and that question</p><p>that begs us all</p><p>at the traffic lights</p><p>and in the circles</p><p>where we buy books and burgers</p><p>and sometimes</p><p>cigarettes</p><p>cloth and loathe that burn</p><p>not quite scattered</p><p>just torn</p><p>worn thorns</p><p>almost ashes</p><p>circling around us all</p><p>the vultures free</p><p>the human needs</p><p>aggressive me</p><p>passive we</p><p>don’t fall asleep</p><p>we need to eat</p><p>don’t dare dream</p><p>hand squeeze me</p><p>all itches unwelcome</p><p>undone</p><p>undusted</p><p>free</p><p>unburdened by the need to know the means</p><p>of how we could kiss fire</p><p>and still be seen</p><p>like a viking</p><p>or a cave painting</p><p>waiting</p><p>for the accountant</p><p>patient</p><p>consoling time with numbers</p><p>a practice</p><p>ancient</p><p>wait then</p><p>for what</p><p>a payslip to heaven</p><p>but it feels like i bought a lemon</p><p>truth</p><p>hard</p><p>like bedrock in a hotel</p><p>so near</p><p>it could almost be home</p><p>but not here</p><p>not like this</p><p>not alone</p><p>waiting</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=not%20like%20this&url=https://sexandloveletters.com/poems/not-like-this.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/not-like-this.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="miss-you.html" class="text-accent hover:underline">← miss you</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="reckoning_.html" class="text-accent hover:underline">reckoning  →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>