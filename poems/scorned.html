<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>scorned | Sex & Love Letters</title>
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/scorned.html">
    <meta property="og:title" content="scorned | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/scorned.html">
    <meta property="twitter:title" content="scorned | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
        
        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .poem-content p {
            margin-bottom: 0.7em;
        }
        
        .letter-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .letter-content p {
            margin-bottom: 0.7em;
        }
        
        .story-content {
            white-space: pre-wrap;
            line-height: 1.7;
        }
        
        .story-content h2 {
            margin-top: 2em;
            margin-bottom: 1em;
            font-size: 1.5rem;
        }
        
        .story-content p {
            margin-bottom: 1em;
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">scorned</h2>
            
            
            
            <div class="poem-content my-8">
                <p>you do not get to call me now</p><p>with your ash-laced tongue</p><p>and trembling hands</p><p>and think I will forget</p><p>how you watched me burn</p><p>you say I almost saw the worst of you</p><p>darling</p><p>I did</p><p>you just didn’t look close enough</p><p>to see me</p><p>sifting through the wreckage</p><p>for the pieces you left behind</p><p>I moved mountains for you</p><p>crossed cities like rivers</p><p>hands open</p><p>heart raw</p><p>asking only</p><p>to be met with warmth</p><p>but you</p><p>you made ice look like mercy</p><p>don’t dress it up as love</p><p>what you gave me</p><p>was silence</p><p>withdrawal</p><p>the kind of absence that bruises</p><p>you broke me open</p><p>then asked why I bled</p><p>and now</p><p>you want to return</p><p>bearing poems and wounds</p><p>as if poetry can exhume</p><p>what you buried</p><p>but I am not your penance</p><p>not your absolution</p><p>not a stop on your pilgrimage</p><p>to self-awareness</p><p>I am the aftermath</p><p>that chose to bloom</p><p>the woman who rose</p><p>from your undoing</p><p>and stitched her name</p><p>back into her skin</p><p>so no</p><p>you do not get to say</p><p>I scared you</p><p>like it’s a compliment</p><p>you should be scared</p><p>not of me</p><p>but of how deeply I learned</p><p>to live</p><p>without your kind of love</p><p>go</p><p>carry your guilt</p><p>like I carried your absence</p><p>let it teach you</p><p>how heavy silence can be</p><p>I don’t want revenge</p><p>I don’t want forgiveness</p><p>I want peace</p><p>the kind that doesn’t wince</p><p>when someone says my name</p><p>and I will find it</p><p>not because you gave me closure</p><p>but because I stopped</p><p>waiting for you to</p>
            </div>
            
            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=scorned&url=https://sexandloveletters.com/poems/scorned.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/scorned.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>
            
            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="safely-leave.html" class="text-accent hover:underline">← safely leave</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="she-lives-here.html" class="text-accent hover:underline">she lives here →</a>
            </div>
        </div>
    </main>

    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
</body>
</html>