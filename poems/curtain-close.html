<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>curtain close | Sex & Love Letters</title>
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/curtain-close.html">
    <meta property="og:title" content="curtain close | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/curtain-close.html">
    <meta property="twitter:title" content="curtain close | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
        
        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .poem-content p {
            margin-bottom: 0.7em;
        }
        
        .letter-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        
        .letter-content p {
            margin-bottom: 0.7em;
        }
        
        .story-content {
            white-space: pre-wrap;
            line-height: 1.7;
        }
        
        .story-content h2 {
            margin-top: 2em;
            margin-bottom: 1em;
            font-size: 1.5rem;
        }
        
        .story-content p {
            margin-bottom: 1em;
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">curtain close</h2>
            
            
            
            <div class="poem-content my-8">
                <p>its cozy in here with the curtains closed</p><p>i know what's outside but the weather's bad</p><p>and you seem comfortable, tv and tea</p><p>so I won't fuck with the moment</p><p>you pull the rug from underneath me</p><p>and ask me to see</p><p>just what i've done and what i've been</p><p>i hear ya, and i want to hide</p><p>wish i pulled the fucking curtains</p><p>so you could see</p><p>just what’s outside and what i am protecting myself from</p><p>but i was too cowardly to say "do you see?"</p><p>so fucking hate me </p><p>then</p><p>i can't stand me neither</p><p>when</p><p>i can't stand up</p><p>because </p><p>i'm so screwed up</p><p>but jesus, some gentle please</p><p>man, i didn't ask for this shit either</p><p>complain away</p><p>i hear you say</p><p>don't whimper</p><p>don't cower</p><p>don't suffer,</p><p>elsewhere, not in front of me</p><p>i never hated you</p><p>i hated this, whatever </p><p>this. is. not. a life, and</p><p>your ego said: i hate you</p><p>i'm so selfish i couldn't see</p><p>how selfish i was</p><p>to you</p><p>when you needed me</p><p>when i needed you</p><p>narcissist, you hiss</p><p>and i smile</p><p>that cigarette, you know it'll kill you?</p><p>in the end</p><p>so i light up</p><p>covered in gas</p><p>selfish way to end</p><p>nevermind the pain</p><p>how could i </p><p>do it </p><p>in front of you</p><p>selfish, through and through</p><p>i didn't hide</p><p>i smirked</p><p>scoffed</p><p>snide</p><p>in the car park</p><p>alone</p><p>with my pride</p><p>i tried</p><p>selfish</p><p>bottom feeding shellfish</p><p>deadweight asshole</p><p>with a smile</p><p>that’s me baby</p><p>but don’t you fucking dare</p><p>accuse me of lies</p><p>i’m the wreck you romanticized</p><p>open those curtains</p><p>and you'll see pain</p><p>in your eyes</p><p>what you feel is not me</p><p>i was your shield</p><p>your rayban love</p><p>it's real baby</p><p>it's real my baby</p><p>i will hold you through this my baby</p><p>i can hold this my baby</p><p>i can hold this</p><p>i can take this</p><p>it's ok my baby</p><p>it's ok baby</p><p>you're ok, baby</p><p>it's ok </p><p>i’ve got you</p><p>i’ll be the curtain to close, and wrap you up warm</p><p>and keep you safe from me</p><p>i was real,</p><p>baby</p>
            </div>
            
            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=curtain%20close&url=https://sexandloveletters.com/poems/curtain-close.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/curtain-close.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>
            
            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="cry-kitten-roar.html" class="text-accent hover:underline">← cry kitten roar</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="delorean.html" class="text-accent hover:underline">delorean →</a>
            </div>
        </div>
    </main>

    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
</body>
</html>