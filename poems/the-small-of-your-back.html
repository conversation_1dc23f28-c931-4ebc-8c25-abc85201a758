<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>the small of your back | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/the-small-of-your-back.html">
    <meta property="og:title" content="the small of your back | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/the-small-of-your-back.html">
    <meta property="twitter:title" content="the small of your back | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }

        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .poem-content p {
            margin-bottom: 0.7em;
        }

        
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <svg class="w-5 h-5 mr-1" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M2 2v2c6.627 0 12 5.373 12 12h2C16 9.373 9.627 2 2 2z"/>
                        <path d="M2 6v2c4.418 0 8 3.582 8 8h2c0-5.523-4.477-10-10-10z"/>
                        <circle cx="3" cy="13" r="1.5"/>
                    </svg>
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute right-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">the small of your back</h2>

            <div class="poem-content my-8">
                <p>the small of your back</p><p>the nape of your neck</p><p>when you tuck your hair behind your ears</p><p>or when your jaw tenses as you think</p><p>try telling that to guys when they ask</p><p>"boobs or legs?"</p><p>fucking idiots</p><p>i'm the "the spaces in between you'll never get to see" man</p><p>"she's hot, but a fish in bed"</p><p>christ, that's rude</p><p>like you forgot how lucky you were</p><p>that she even got into bed with you</p><p>but that's how sex works in this show</p><p>so much free porn</p><p>tv shows that blow</p><p>up</p><p>(no, not that blow)</p><p>men who think not getting laid means they're broken</p><p>women who think men are pretty in white</p><p>nobody is pretty</p><p>but tenderness is sweet</p><p>and sometimes, someone</p><p>comes along, after all your loneliness</p><p>and gingerly gives you that kiss</p><p>that lets you know</p><p>i'm a leg man</p><p>almost had you there</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=the%20small%20of%20your%20back&url=https://sexandloveletters.com/poems/the-small-of-your-back.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/the-small-of-your-back.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="teeth-part-2.html" class="text-accent hover:underline">← teeth (part 2)</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="to-the-one-who-never-touched-me.html" class="text-accent hover:underline">to the one who never touched me →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <div class="mt-8 flex items-center justify-center gap-2 text-sm text-gray-400">
            <svg class="w-4 h-4" viewBox="0 0 16 16" fill="currentColor">
                <path d="M2 2v2c6.627 0 12 5.373 12 12h2C16 9.373 9.627 2 2 2z"/>
                <path d="M2 6v2c4.418 0 8 3.582 8 8h2c0-5.523-4.477-10-10-10z"/>
                <circle cx="3" cy="13" r="1.5"/>
            </svg>
            <span>Stay updated via <a href="../feeds/main.xml" class="text-accent hover:underline">RSS</a></span>
        </div>
        <div class="mt-6 flex items-center justify-center gap-4">
            <a href="https://mastodon.social/@jonathanopperman" target="_blank" rel="me noopener" class="flex items-center gap-2 text-sm text-gray-400 hover:text-accent transition-colors">
                <img src="../assets/cicada.png" alt="Follow on Mastodon" class="w-5 h-5">
                <span>Follow on Mastodon</span>
            </a>
            <a href="#" onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : alert('Sharing not supported')" class="flex items-center gap-2 text-sm text-gray-400 hover:text-accent transition-colors">
                <img src="../assets/cicada.png" alt="Share" class="w-5 h-5">
                <span>Share</span>
            </a>
        </div>
        <p class="mt-8">&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>