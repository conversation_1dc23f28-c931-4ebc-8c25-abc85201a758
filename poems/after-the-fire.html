<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>after the fire | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/poems/after-the-fire.html">
    <meta property="og:title" content="after the fire | Sex & Love Letters">
    <meta property="og:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/poems/after-the-fire.html">
    <meta property="twitter:title" content="after the fire | Sex & Love Letters">
    <meta property="twitter:description" content="A poem from Sex & Love Letters exploring the depths of human connection and emotion.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }

        .poem-content {
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .poem-content p {
            margin-bottom: 0.7em;
        }

        
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">after the fire</h2>

            <div class="poem-content my-8">
                <p>my hands tremble as i stand</p><p>invisible but not divisible</p><p>this room, you left me empty in</p><p>i won't call for you, anymore</p><p>i put words in your mouth</p><p>because yours don't come out</p><p>you love this silence</p><p>you use it like a punishment</p><p>you dish it</p><p>to keep me out</p><p>of your mind</p><p>i've lost mine</p><p>and i smile</p><p>because i hold my own truth</p><p>the last thing, i couldn't lose</p><p>and from this moment</p><p>i am free</p><p>i let the little foxes free</p><p>so they can roam and croon</p><p>in fields that bloom</p><p>with happiness, fleeting</p><p>i let go</p><p>i was fighting too fiercely</p><p>because it meant something</p><p>but it meant only to me</p><p>what it means to you</p><p>is not my monkey</p><p>not my voices</p><p>when i wanted to say</p><p>it's ok to be</p><p>angry</p><p>at me</p><p>deny me</p><p>in your history</p><p>that's ok</p><p>i did this to you</p><p>you say</p><p>the soft</p><p>the gentle</p><p>was always me</p><p>but caged and afraid</p><p>i went for you</p><p>snarling, as scared cats do</p><p>i forgive me</p><p>i forgive you</p><p>i forgive the world i wanted to see burn</p><p>because it took from me</p><p>and you</p><p>and i saw myself in the Colosseum</p><p>sweared by my hairs for you</p><p>because i thought</p><p>it was the right thing, to do</p><p>i was wrong, i've been so wrong</p><p>all along</p><p>so i say this gently</p><p>i want to make the bed</p><p>the way you did</p><p>with care and grace</p><p>as if it was a holy ritual</p><p>and every scoff</p><p>you heard from me</p><p>i remember saying</p><p>all of it</p><p>like hurt i made and gave</p><p>in a grave i will keep it</p><p>don't let them ceremonize it</p><p>i ask for nothing on the tombstone</p><p>leave it blank</p><p>for graffiti to say, i was here</p><p>you</p><p>didn't deserve it</p><p>when i became unnerved</p><p>and you tried to be kind</p><p>but i tested your patience</p><p>which you gave to me</p><p>in abundance</p><p>my apology is too late</p><p>i know</p><p>i'm on the road, always</p><p>i end writing to silence</p><p>letters into the wind</p><p>poems like amateur mist-y</p><p>tears</p><p>fears</p><p>i fear nothing now</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=after%20the%20fire&url=https://sexandloveletters.com/poems/after-the-fire.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/poems/after-the-fire.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <a href="1714.html" class="text-accent hover:underline">← 1714</a>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="and-still-the-light-came-in.html" class="text-accent hover:underline">and still the light came in →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>