#!/bin/bash
# Rollback script in case the Docker transition has issues

echo "Rolling back to the original nginx setup..."

# Step 1: Stop the Docker container
echo "Stopping Docker container..."
cd /var/www/sexandloveletters.com
docker-compose down

# Step 2: Start the system nginx service
echo "Starting system nginx service..."
sudo systemctl start nginx

# Step 3: Verify nginx is running
echo "Verifying nginx status..."
sudo systemctl status nginx | grep Active

echo ""
echo "Rollback completed!"
echo "Your site should now be running from the original nginx setup on port 80."
echo "If you need to try the Docker setup again, run the transition_to_docker.sh script."
