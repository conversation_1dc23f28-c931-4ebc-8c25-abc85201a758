#!/bin/bash
# Transition script for moving sexandloveletters.com to Docker

# Make sure we're in the right directory
cd /var/www/sexandloveletters.com

# Step 1: Make the backup script executable and run it
chmod +x backup_before_docker.sh
./backup_before_docker.sh

# Step 2: Stop the existing Docker container if it's running
echo "Stopping existing Docker container..."
docker-compose down

# Step 3: Stop the system nginx service
echo "Stopping system nginx service..."
sudo systemctl stop nginx

# Step 4: Rebuild and start the Docker container
echo "Building and starting Docker container..."
docker-compose up --build -d

# Step 5: Verify the Docker container is running
echo "Verifying container status..."
docker-compose ps

# Step 6: Test if the site is accessible
echo "Testing site accessibility..."
curl -s -I http://localhost:80 | head -10

echo ""
echo "Transition completed!"
echo "Your site should now be running from the Docker container on port 80."
echo ""
echo "If you need to revert back to the original setup:"
echo "1. Run: docker-compose down"
echo "2. Run: sudo systemctl start nginx"
