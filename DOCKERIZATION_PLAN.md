# Multi-Site Dockerization Plan

This document outlines the strategy for dockerizing multiple websites running on a single server.

## Current Infrastructure

- Nginx web server hosting multiple sites
- Sites are located in `/var/www/` directory
- Each site has its own domain and configuration

## Dockerization Strategy

### Step 1: Containerize Each Website Individually

For each website:
1. Create a Dockerfile specific to the site's requirements
2. Create a docker-compose.yml file for easy management
3. Test the containerized site locally

### Step 2: Set Up a Reverse Proxy

1. Install and configure a reverse proxy (Nginx Proxy Manager or Traefik)
2. Create a Docker network for all containers to communicate
3. Configure the reverse proxy to route traffic to the appropriate container

### Step 3: Migrate Sites One by One

1. Start with non-critical sites (like sexandloveletters.com)
2. Test thoroughly before moving to the next site
3. Keep the original setup running until confident in the Docker setup

## Implementation for All Sites

### Create a Docker Network

```bash
docker network create web-proxy
```

### Set Up Nginx Proxy Manager

```yaml
# docker-compose.yml for Nginx Proxy Manager
version: '3'

services:
  nginx-proxy-manager:
    image: 'jc21/nginx-proxy-manager:latest'
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
      - '81:81'  # Admin UI
    volumes:
      - ./data:/data
      - ./letsencrypt:/etc/letsencrypt
    networks:
      - web-proxy

networks:
  web-proxy:
    external: true
```

### Update Each Site's docker-compose.yml

```yaml
# Example for sexandloveletters.com
version: '3'

services:
  sexandloveletters:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sexandloveletters
    restart: unless-stopped
    networks:
      - web-proxy
    # No need to expose ports publicly, the proxy will handle this

networks:
  web-proxy:
    external: true
```

### Configure in Nginx Proxy Manager

1. Access the Admin UI at http://your-server-ip:81
2. Add a new Proxy Host:
   - Domain: sexandloveletters.com
   - Scheme: http
   - Forward Hostname: sexandloveletters
   - Forward Port: 80
   - Configure SSL if needed

## Benefits

- Each site is isolated with its own container
- Easy to update individual sites without affecting others
- Consistent environment for all sites
- Simplified deployment and scaling
- Better resource management and monitoring

## Next Steps After Full Migration

1. Set up monitoring with tools like Prometheus and Grafana
2. Implement automated backups
3. Create CI/CD pipelines for automatic deployment
4. Configure container orchestration for high availability
