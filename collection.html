<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collection | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexandloveletters.com/collection.html">
    <meta property="og:title" content="Collection - Sex & Love Letters">
    <meta property="og:description" content="Explore the complete collection of poems, love letters, and short stories.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <meta property="og:site_name" content="Sex & Love Letters">
    <meta property="og:locale" content="en_US">
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@jonathanopperman">
    <meta name="twitter:creator" content="@jonathanopperman">
    <meta name="twitter:url" content="https://sexandloveletters.com/collection.html">
    <meta name="twitter:title" content="Collection - Sex & Love Letters">
    <meta name="twitter:description" content="Explore the complete collection of poems, love letters, and short stories.">
    <meta name="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta name="twitter:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
    </style>
    <script>
        // Enhanced search functionality with content type filtering
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const filterTypeSelect = document.getElementById('filter-type');
            const poemLinks = document.querySelectorAll('.poems-grid a');
            const letterLinks = document.querySelectorAll('.letters-grid a');
            const storyLinks = document.querySelectorAll('.stories-grid a');
            const poemsSection = document.getElementById('poems-section');
            const lettersSection = document.getElementById('letters-section');
            const storiesSection = document.getElementById('stories-section');
            const noResultsEl = document.getElementById('no-results');
            
            function performSearch() {
                const query = searchInput.value.toLowerCase().trim();
                const filterType = filterTypeSelect.value;
                let poemMatches = 0;
                let letterMatches = 0;
                let storyMatches = 0;
                
                // Determine visibility based on filter type
                const showPoems = filterType === 'all' || filterType === 'poem';
                const showLetters = filterType === 'all' || filterType === 'letter';
                const showStories = filterType === 'all' || filterType === 'story';
                
                // Filter poem links
                if (showPoems) {
                    poemLinks.forEach(link => {
                        const title = link.textContent.toLowerCase();
                        if (query === '' || title.includes(query)) {
                            link.classList.remove('hidden');
                            poemMatches++;
                        } else {
                            link.classList.add('hidden');
                        }
                    });
                } else {
                    // Hide all poems when filtered out
                    poemLinks.forEach(link => link.classList.add('hidden'));
                }
                
                // Filter letter links
                if (showLetters) {
                    letterLinks.forEach(link => {
                        const title = link.textContent.toLowerCase();
                        if (query === '' || title.includes(query)) {
                            link.classList.remove('hidden');
                            letterMatches++;
                        } else {
                            link.classList.add('hidden');
                        }
                    });
                } else {
                    // Hide all letters when filtered out
                    letterLinks.forEach(link => link.classList.add('hidden'));
                }
                
                // Filter story links
                if (showStories) {
                    storyLinks.forEach(link => {
                        const title = link.textContent.toLowerCase();
                        if (query === '' || title.includes(query)) {
                            link.classList.remove('hidden');
                            storyMatches++;
                        } else {
                            link.classList.add('hidden');
                        }
                    });
                } else {
                    // Hide all stories when filtered out
                    storyLinks.forEach(link => link.classList.add('hidden'));
                }
                
                // Show/hide sections based on results and filter
                poemsSection.classList.toggle('hidden', poemMatches === 0 || !showPoems);
                lettersSection.classList.toggle('hidden', letterMatches === 0 || !showLetters);
                storiesSection.classList.toggle('hidden', storyMatches === 0 || !showStories);
                
                // Show/hide no results message
                noResultsEl.classList.toggle('hidden', (poemMatches > 0 && showPoems) || (letterMatches > 0 && showLetters) || (storyMatches > 0 && showStories) || (query === '' && filterType === 'all'));
            }
            
            // Add event listeners
            searchInput.addEventListener('input', performSearch);
            filterTypeSelect.addEventListener('change', performSearch);
        });
    </script>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="index.html">
            <img src="assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="collection.html" class="text-accent hover:underline">Collection</a>
            <a href="about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true" id="subscribeButton" onclick="toggleDropdown(event)">
                    <img src="assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </a>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-2xl">
            <p class="text-center text-gray-400 text-sm mb-8">Search and browse all poems, letters, and stories</p>
            
            <!-- Search and Filter -->
            <div class="mb-12">
                <div class="flex flex-col md:flex-row gap-4 max-w-lg mx-auto">
                    <div class="relative flex-grow">
                        <input 
                            type="text" 
                            id="search" 
                            placeholder="Search by title..." 
                            class="w-full px-4 py-2 bg-gray-900 border border-gray-700 text-white rounded-md focus:outline-none focus:border-accent"
                        >
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                    <select 
                        id="filter-type" 
                        class="bg-gray-900 border border-gray-700 text-white rounded-md py-2 px-4 focus:outline-none focus:border-accent"
                    >
                        <option value="all">All Content</option>
                        <option value="poem">Poems Only</option>
                        <option value="letter">Letters Only</option>
                        <option value="story">Stories Only</option>
                    </select>
                </div>
            </div>
            
            <!-- No results message -->
            <div id="no-results" class="text-center py-8 hidden">
                <p class="text-gray-400">No poems, letters, or stories match your search.</p>
            </div>
            
            <div id="poems-section">
                <h2 class="text-xl font-normal mb-8 text-center">Poems</h2>
                
                <div class="poems-grid grid grid-cols-1 md:grid-cols-2 gap-4 mb-16">
                <a href="poems/1714.html" class="hover:text-accent transition-colors">1714</a>
                <a href="poems/after-the-fire.html" class="hover:text-accent transition-colors">after the fire</a>
                <a href="poems/and-still-the-light-came-in.html" class="hover:text-accent transition-colors">and still the light came in</a>
                <a href="poems/cicadas.html" class="hover:text-accent transition-colors">cicadas</a>
                <a href="poems/cry-kitten-roar.html" class="hover:text-accent transition-colors">cry kitten roar</a>
                <a href="poems/curtain-close.html" class="hover:text-accent transition-colors">curtain close</a>
                <a href="poems/delorean.html" class="hover:text-accent transition-colors">delorean</a>
                <a href="poems/dog.html" class="hover:text-accent transition-colors">dog</a>
                <a href="poems/economically.html" class="hover:text-accent transition-colors">economically</a>
                <a href="poems/even-in-hell.html" class="hover:text-accent transition-colors">even in hell</a>
                <a href="poems/finding-where-we-laughed.html" class="hover:text-accent transition-colors">finding where we laughed</a>
                <a href="poems/fingertips-at-my-throat.html" class="hover:text-accent transition-colors">fingertips at my throat</a>
                <a href="poems/i-kiss-you.html" class="hover:text-accent transition-colors">i kiss you</a>
                <a href="poems/i-met-the-god-inside-me-and-she-was-not-kind.html" class="hover:text-accent transition-colors">i met the god inside me and she was not kind</a>
                <a href="poems/im-a-leg-man.html" class="hover:text-accent transition-colors">im a leg man</a>
                <a href="poems/miss-you.html" class="hover:text-accent transition-colors">miss you</a>
                <a href="poems/not-like-this.html" class="hover:text-accent transition-colors">not like this</a>
                <a href="poems/reckoning_.html" class="hover:text-accent transition-colors">reckoning </a>
                <a href="poems/safely-leave.html" class="hover:text-accent transition-colors">safely leave</a>
                <a href="poems/scorned.html" class="hover:text-accent transition-colors">scorned</a>
                <a href="poems/she-lives-here.html" class="hover:text-accent transition-colors">she lives here</a>
                <a href="poems/signal-noise.html" class="hover:text-accent transition-colors">signal noise</a>
                <a href="poems/tabs.html" class="hover:text-accent transition-colors">tabs</a>
                <a href="poems/teeth-part-1.html" class="hover:text-accent transition-colors">teeth (part 1)</a>
                <a href="poems/teeth-part-2.html" class="hover:text-accent transition-colors">teeth (part 2)</a>
                <a href="poems/the-small-of-your-back.html" class="hover:text-accent transition-colors">the small of your back</a>
                <a href="poems/to-the-one-who-never-touched-me.html" class="hover:text-accent transition-colors">to the one who never touched me</a>
                <a href="poems/what-remains.html" class="hover:text-accent transition-colors">what remains</a>
                <a href="poems/what we don_t say.html" class="hover:text-accent transition-colors">what we don't say</a>
                <a href="poems/when-i-could-not-speak.html" class="hover:text-accent transition-colors">when i could not speak</a>
                <a href="poems/wrapped.html" class="hover:text-accent transition-colors">wrapped</a>
                <a href="poems/yesterday.html" class="hover:text-accent transition-colors">yesterday</a>
            </div>
            </div>
            
            <div id="letters-section">
                <h2 class="text-xl font-normal mb-8 text-center">Love Letters</h2>
                
                <div class="letters-grid grid grid-cols-1 md:grid-cols-2 gap-4 mb-16">
                    <a href="letters/little-gecko.html" class="hover:text-accent transition-colors">Little Gecko</a>
                    <a href="letters/mei-mei.html" class="hover:text-accent transition-colors">Mei-mei</a>
                </div>
            </div>
            
            <div id="stories-section">
                <h2 class="text-xl font-normal mb-8 text-center">Short Stories</h2>
                
                <div class="stories-grid grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="stories/umqombothi.html" class="hover:text-accent transition-colors">The Secret to Mama Cebisa's Best Umqombothi</a>
                </div>
            </div>
            
            <div id="rss-section" class="mt-16 border-t border-gray-800 pt-8">
                <h2 class="text-xl font-normal mb-4 text-center">Stay Connected</h2>
                               
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-lg mx-auto mb-8">
                    <a href="feeds/main.xml" class="flex items-center justify-center gap-2 border border-gray-800 p-3 rounded hover:border-gray-700 hover:text-accent transition-colors">
                        <img src="assets/rss-icon.svg" alt="RSS" class="w-5 h-5">
                        <span>All Content</span>
                    </a>
                    <a href="feeds/poems.xml" class="flex items-center justify-center gap-2 border border-gray-800 p-3 rounded hover:border-gray-700 hover:text-accent transition-colors">
                        <img src="assets/rss-icon.svg" alt="RSS" class="w-5 h-5">
                        <span>Poems Only</span>
                    </a>
                    <a href="feeds/letters.xml" class="flex items-center justify-center gap-2 border border-gray-800 p-3 rounded hover:border-gray-700 hover:text-accent transition-colors">
                        <img src="assets/rss-icon.svg" alt="RSS" class="w-5 h-5">
                        <span>Letters Only</span>
                    </a>
                    <a href="feeds/stories.xml" class="flex items-center justify-center gap-2 border border-gray-800 p-3 rounded hover:border-gray-700 hover:text-accent transition-colors">
                        <img src="assets/rss-icon.svg" alt="RSS" class="w-5 h-5">
                        <span>Stories Only</span>
                    </a>
                </div>

                <div class="flex items-center justify-center gap-6 mt-8">
                    <a href="https://mastodon.social/@jonathanopperman" target="_blank" rel="me noopener" class="flex items-center gap-2 text-sm text-gray-400 hover:text-accent transition-colors">
                        <img src="assets/cicada.png" alt="Follow on Mastodon" class="w-5 h-5">
                        <span>Follow on Mastodon</span>
                    </a>
                    <button onclick="shareContent()" class="flex items-center gap-2 text-sm text-gray-400 hover:text-accent transition-colors">
                        <img src="assets/cicada.png" alt="Share" class="w-5 h-5">
                        <span>Share</span>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="w-full max-w-3xl text-center py-8 text-xs text-gray-500 mt-16">
        <p>&copy; 2025 • <a href="mailto:<EMAIL>" class="hover:text-accent transition-colors">Jonathan Opperman</a> • All Rights Reserved</p>
    </footer>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById('subscribeDropdown');
            dropdown.classList.toggle('hidden');
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                const subscribeButton = document.getElementById('subscribeButton');
                if (e.target !== subscribeButton && !subscribeButton.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.classList.add('hidden');
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }

        // Function to share content
        function shareContent() {
            if (navigator.share) {
                navigator.share({
                    title: 'Collection | Sex & Love Letters',
                    text: 'Browse a curated collection of poems, love letters, and short stories exploring intimacy, desire, and vulnerability.',
                    url: window.location.href
                }).catch(console.error);
            } else {
                // Fallback for browsers that don't support the Web Share API
                const url = encodeURIComponent(window.location.href);
                const text = encodeURIComponent('Check out the collection at Sex & Love Letters - A curated collection of poems, love letters, and short stories exploring intimacy, desire, and vulnerability.');
                window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
            }
        }
    </script>
</body>
</html>
