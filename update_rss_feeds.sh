#!/bin/bash
# <PERSON><PERSON>t to update RSS feeds in the Docker container

echo "Updating RSS feeds in Docker container..."

# Step 1: Regenerate RSS feeds
cd /var/www/sexandloveletters.com
node generate-rss.js

# Step 2: Rebuild and restart the Docker container
docker-compose down
docker-compose up --build -d

echo "RSS feeds have been updated and Docker container restarted."
echo "You can access the RSS feeds at:"
echo "- https://sexandloveletters.com/feeds/main.xml"
echo "- https://sexandloveletters.com/feeds/poems.xml"
echo "- https://sexandloveletters.com/feeds/letters.xml"
echo "- https://sexandloveletters.com/feeds/stories.xml"
