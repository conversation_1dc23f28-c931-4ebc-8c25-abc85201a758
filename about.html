<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexandloveletters.com/about.html">
    <meta property="og:title" content="About | Sex & Love Letters">
    <meta property="og:description" content="About the collection of poems, love letters, and short stories exploring the raw intersection of desire, intimacy, and emotional exposure.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <meta property="og:site_name" content="Sex & Love Letters">
    <meta property="og:locale" content="en_US">
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@jonathanopperman">
    <meta name="twitter:creator" content="@jonathanopperman">
    <meta name="twitter:url" content="https://sexandloveletters.com/about.html">
    <meta name="twitter:title" content="About | Sex & Love Letters">
    <meta name="twitter:description" content="About the collection of poems, love letters, and short stories exploring the raw intersection of desire, intimacy, and emotional exposure.">
    <meta name="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta name="twitter:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="index.html">
            <img src="assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="collection.html" class="hover:text-accent transition-colors">Collection</a>
            <a href="about.html" class="text-accent hover:underline">About</a>
            <div class="relative group">
                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true" id="subscribeButton" onclick="toggleDropdown(event)">
                    <svg class="w-5 h-5 mr-1" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M2 2v2c6.627 0 12 5.373 12 12h2C16 9.373 9.627 2 2 2z"/>
                        <path d="M2 6v2c4.418 0 8 3.582 8 8h2c0-5.523-4.477-10-10-10z"/>
                        <circle cx="3" cy="13" r="1.5"/>
                    </svg>
                    <span>Subscribe</span>
                </a>
                <div id="subscribeDropdown" class="absolute right-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg prose prose-invert prose-red">
            <h2 class="text-xl font-normal mb-6 text-center">About this Collection</h2>
            
            <p class="my-6">
                "Sex & Love Letters" is a collection that explores the raw intersection of desire, intimacy, and emotional exposure. 
                These pieces traverse the landscape of human connection, from its most tender expressions to its most visceral wounds.
            </p>
            
            <p class="my-6">
                Each poem, letter, and short story is an artifact of vulnerability, a testament to the power of putting words to 
                feelings that often remain unspoken. They are offerings to that space between bodies and souls 
                where we find both our deepest connections and most profound alienations.
            </p>
            
            <p class="my-6">
                The cicada, an emblem of emergence after long periods of darkness, symbolizes the cyclical nature of 
                love, loss, and rebirth that threads through this collection.
            </p>
            
            <p class="my-6">
                Thank you for witnessing these words. - J
            </p>

            <div class="mt-12 flex items-center justify-center gap-6">
                <a href="https://mastodon.social/@jonathanopperman" target="_blank" rel="me noopener" class="flex items-center gap-2 text-sm text-gray-400 hover:text-accent transition-colors">
                    <img src="assets/cicada.png" alt="Follow on Mastodon" class="w-5 h-5">
                    <span>Follow on Mastodon</span>
                </a>
                <button onclick="shareContent()" class="flex items-center gap-2 text-sm text-gray-400 hover:text-accent transition-colors">
                    <img src="assets/cicada.png" alt="Share" class="w-5 h-5">
                    <span>Share</span>
                </button>
            </div>

        </div>
    </main>

    <footer class="w-full max-w-3xl text-center py-8 text-xs text-gray-500">
        <p>&copy; 2025 • <a href="mailto:<EMAIL>" class="hover:text-accent transition-colors">Jonathan Opperman</a> • All Rights Reserved</p>
    </footer>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById('subscribeDropdown');
            dropdown.classList.toggle('hidden');
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                const subscribeButton = document.getElementById('subscribeButton');
                if (e.target !== subscribeButton && !subscribeButton.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.classList.add('hidden');
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }

        // Function to share content
        function shareContent() {
            if (navigator.share) {
                navigator.share({
                    title: 'About | Sex & Love Letters',
                    text: 'About the collection of poems, love letters, and short stories exploring the raw intersection of desire, intimacy, and emotional exposure.',
                    url: window.location.href
                }).catch(console.error);
            } else {
                // Fallback for browsers that don't support the Web Share API
                const url = encodeURIComponent(window.location.href);
                const text = encodeURIComponent('About Sex & Love Letters - A collection of poems, love letters, and short stories exploring the raw intersection of desire, intimacy, and emotional exposure.');
                window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
            }
        }
    </script>
</body>
</html>
