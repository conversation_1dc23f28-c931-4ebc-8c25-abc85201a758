#!/bin/bash
# <PERSON>ript to update the generate.js template with RSS links

# Define the pattern and replacement strings
READ_PATTERN='    <meta property="twitter:description" content="\${type === '"'"'poem'"'"' ? '"'"'A poem from Sex & Love Letters exploring the depths of human connection and emotion.'"'"' : type === '"'"'letter'"'"' ? '"'"'A love letter exploring intimacy, desire, and vulnerability from Sex & Love Letters.'"'"' : '"'"'A short story from Sex & Love Letters exploring the complexities of human relationships and experience.'"'"'}">\n    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">'
WRITE_PATTERN='    <meta property="twitter:description" content="\${type === '"'"'poem'"'"' ? '"'"'A poem from Sex & Love Letters exploring the depths of human connection and emotion.'"'"' : type === '"'"'letter'"'"' ? '"'"'A love letter exploring intimacy, desire, and vulnerability from Sex & Love Letters.'"'"' : '"'"'A short story from Sex & Love Letters exploring the complexities of human relationships and experience.'"'"'}">\n    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">\n    <!-- RSS Feeds -->\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">'

# Define the navigation pattern and replacement strings
NAV_PATTERN='        <nav class="flex gap-6 md:gap-12 text-lg mb-16">\n            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>\n            <a href="../collection.html" class="text-accent hover:underline">Collection</a>\n            <a href="../about.html" class="hover:text-accent transition-colors">About</a>\n        </nav>'
NAV_REPLACEMENT='        <nav class="flex gap-6 md:gap-12 text-lg mb-16">\n            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>\n            <a href="../collection.html" class="text-accent hover:underline">Collection</a>\n            <a href="../about.html" class="hover:text-accent transition-colors">About</a>\n            <div class="relative group">\n                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true" id="subscribeButton" onclick="toggleDropdown(event)">\n                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">\n                    <span>Subscribe</span>\n                </a>\n                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">\n                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>\n                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>\n                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>\n                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>\n                </div>\n            </div>\n        </nav>'

# Make a backup of the file before editing
cp /var/www/sexandloveletters.com/generate.js /var/www/sexandloveletters.com/generate.js.bak

# Update the generate.js file
sed -i "s|$READ_PATTERN|$WRITE_PATTERN|g" /var/www/sexandloveletters.com/generate.js
sed -i "s|$NAV_PATTERN|$NAV_REPLACEMENT|g" /var/www/sexandloveletters.com/generate.js

# Now update the generate-stories.js file if it has a similar template
if [ -f /var/www/sexandloveletters.com/generate-stories.js ]; then
  cp /var/www/sexandloveletters.com/generate-stories.js /var/www/sexandloveletters.com/generate-stories.js.bak
  sed -i "s|$READ_PATTERN|$WRITE_PATTERN|g" /var/www/sexandloveletters.com/generate-stories.js
  sed -i "s|$NAV_PATTERN|$NAV_REPLACEMENT|g" /var/www/sexandloveletters.com/generate-stories.js
fi

# Add JavaScript function for dropdown
JS_PATTERN='</footer>'
JS_REPLACEMENT='</footer>\n\n    <script>\n        function toggleDropdown(event) {\n            event.preventDefault();\n            const dropdown = document.getElementById("subscribeDropdown");\n            dropdown.classList.toggle("hidden");\n            \n            // Close dropdown when clicking outside\n            const closeDropdown = (e) => {\n                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {\n                    dropdown.classList.add("hidden");\n                    document.removeEventListener("click", closeDropdown);\n                }\n            };\n            document.addEventListener("click", closeDropdown);\n        }\n    </script>'

# Update both template files with the JavaScript function
sed -i "s|$JS_PATTERN|$JS_REPLACEMENT|g" /var/www/sexandloveletters.com/generate.js
if [ -f /var/www/sexandloveletters.com/generate-stories.js ]; then
  sed -i "s|$JS_PATTERN|$JS_REPLACEMENT|g" /var/www/sexandloveletters.com/generate-stories.js
fi

echo "RSS links added to generation templates"
