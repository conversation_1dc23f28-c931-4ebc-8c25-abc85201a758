# Sex & Love Letters - Dockerized

This repository contains a dockerized version of the Sex & Love Letters website.

## Getting Started

These instructions will get you a copy of the project up and running in a Docker container.

### Prerequisites

- Docker
- Docker Compose

### Running the Container

1. Build and start the container:

```bash
docker-compose up -d
```

2. The website should now be accessible at http://localhost:8070

### Updating Content

When you make changes to the site content:

1. Edit the content in the `content/` directory
2. Run the generation script:

```bash
docker exec sexandloveletters npm run generate
```

Or, alternatively, rebuild the container:

```bash
docker-compose down
docker-compose up -d --build
```

### Stopping the Container

```bash
docker-compose down
```

## Managing Multiple Sites

To manage multiple sites with Dock<PERSON>, consider the following best practices:

1. **Use unique container names and port mappings**:
   - Each site should have its own container name and port mapping in its docker-compose.yml

2. **Set up a reverse proxy**:
   - Use a service like <PERSON><PERSON>fi<PERSON> or Nginx Proxy Manager to route traffic to the appropriate container

3. **Create a network for all containers**:
   - Create a shared network to allow containers to communicate

Example reverse proxy setup with multiple sites:

```yaml
# docker-compose.yml for reverse proxy
version: '3'

services:
  nginx-proxy:
    image: jwilder/nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./certs:/etc/nginx/certs
    networks:
      - proxy-network

networks:
  proxy-network:
    external: true
```

Then, for each site:

```yaml
# docker-compose.yml for each site
version: '3'

services:
  sexandloveletters:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sexandloveletters
    environment:
      - VIRTUAL_HOST=sexandloveletters.com,www.sexandloveletters.com
    networks:
      - proxy-network

networks:
  proxy-network:
    external: true
```

## Production Deployment

For production, you should:

1. Set up SSL certificates for HTTPS
2. Use a proper reverse proxy (Nginx, Traefik, etc.)
3. Configure proper logging and monitoring
4. Set up automatic backups of your content

## License

This project is licensed under the ISC License - see the LICENSE file for details.
