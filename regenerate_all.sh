#!/bin/bash
# <PERSON>ript to regenerate all content with updated templates and favicon

echo "Regenerating all website content..."

# Run the content generation scripts
node generate.js

echo "Content regeneration complete!"
echo "New favicon and template changes have been applied to all pages."

# If running in Docker, restart the container
if [ -f "/.dockerenv" ]; then
  echo "Detected Docker environment, restarting container..."
  cd /var/www/sexandloveletters.com
  docker-compose down
  docker-compose up --build -d
  echo "Docker container restarted."
fi

echo "Done!"