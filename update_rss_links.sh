#!/bin/bash
# Script to add RSS links to all HTML files

# Add RSS links to all poem HTML files
for file in /var/www/sexandloveletters.com/poems/*.html; do
  # Add RSS links to head section
  sed -i 's|<meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">\n    <script src="https://cdn.tailwindcss.com">|<meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">\n    <!-- RSS Feeds -->\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">\n    <script src="https://cdn.tailwindcss.com">|' "$file"

  # Update navigation with RSS dropdown
  sed -i 's|<nav class="flex gap-6 md:gap-12 text-lg mb-16">\n            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>\n            <a href="../collection.html" class="text-accent hover:underline">Collection</a>\n            <a href="../about.html" class="hover:text-accent transition-colors">About</a>\n        </nav>|<nav class="flex gap-6 md:gap-12 text-lg mb-16">\n            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>\n            <a href="../collection.html" class="text-accent hover:underline">Collection</a>\n            <a href="../about.html" class="hover:text-accent transition-colors">About</a>\n            <div class="relative group">\n                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">\n                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">\n                    <span>Subscribe</span>\n                </a>\n                <div class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden group-hover:block z-10">\n                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>\n                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>\n                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>\n                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>\n                </div>\n            </div>\n        </nav>|' "$file"
done

# Add RSS links to all letter HTML files
for file in /var/www/sexandloveletters.com/letters/*.html; do
  # Add RSS links to head section
  sed -i 's|<meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">\n    <script src="https://cdn.tailwindcss.com">|<meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">\n    <!-- RSS Feeds -->\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">\n    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">\n    <script src="https://cdn.tailwindcss.com">|' "$file"

  # Update navigation with RSS dropdown
  sed -i 's|<nav class="flex gap-6 md:gap-12 text-lg mb-16">\n            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>\n            <a href="../collection.html" class="text-accent hover:underline">Collection</a>\n            <a href="../about.html" class="hover:text-accent transition-colors">About</a>\n        </nav>|<nav class="flex gap-6 md:gap-12 text-lg mb-16">\n            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>\n            <a href="../collection.html" class="text-accent hover:underline">Collection</a>\n            <a href="../about.html" class="hover:text-accent transition-colors">About</a>\n            <div class="relative group">\n                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">\n                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">\n                    <span>Subscribe</span>\n                </a>\n                <div class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden group-hover:block z-10">\n                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>\n                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>\n                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>\n                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>\n                </div>\n            </div>\n        </nav>|' "$file"
done

echo "RSS links added to all HTML files"
