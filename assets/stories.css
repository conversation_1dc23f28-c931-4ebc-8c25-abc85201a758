/* Stories section styles */
.stories-intro {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 800px;
}

.stories-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.story-card {
    border: 1px solid #eaeaea;
    padding: 1.5rem;
    border-radius: 5px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.story-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.story-card h2 {
    margin-top: 0;
    font-size: 1.5rem;
}

.story-card .excerpt {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #555;
    margin-bottom: 1rem;
}

.story-meta {
    font-size: 0.85rem;
    color: #777;
}

/* Individual story page styles */
.story-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.story-header {
    margin-bottom: 2rem;
    text-align: center;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 1rem;
}

.story-header h2 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
}

.story-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.story-content h3 {
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    font-size: 1.6rem;
}

.story-content p {
    margin-bottom: 1.2rem;
}

.story-content blockquote {
    border-left: 4px solid #ddd;
    padding-left: 1rem;
    margin-left: 0;
    font-style: italic;
}

.story-navigation {
    margin-top: 3rem;
    padding-top: 1rem;
    border-top: 1px solid #eaeaea;
}

.back-to-stories {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #f5f5f5;
    border-radius: 3px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s ease;
}

.back-to-stories:hover {
    background-color: #e0e0e0;
}

@media (max-width: 768px) {
    .stories-list {
        grid-template-columns: 1fr;
    }
    
    .story-header h2 {
        font-size: 1.8rem;
    }
    
    .story-content {
        font-size: 1rem;
    }
}
