/* Production CSS for Sex & Love Letters */
/* Reset and base styles */
*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  margin: 0;
  line-height: inherit;
}

/* Custom properties */
:root {
  --accent: #ff0000;
}

/* Background colors */
.bg-black { background-color: #000000; }
.bg-gray-900 { background-color: #111827; }

/* Text colors */
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-700 { color: #374151; }
.text-accent { color: var(--accent); }

/* Font families */
.font-serif { font-family: Georgia, Cambria, "Times New Roman", Times, serif; }

/* Font sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

/* Font weights */
.font-normal { font-weight: 400; }

/* Layout */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-grow { flex-grow: 1; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }

/* Spacing */
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.p-3 { padding: 0.75rem; }
.p-6 { padding: 1.5rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.mt-16 { margin-top: 4rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-3 { margin-right: 0.75rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-8 { margin-top: 2rem; margin-bottom: 2rem; }

/* Width and height */
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.w-48 { width: 12rem; }
.w-full { width: 100%; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }

/* Max width */
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-12 { gap: 3rem; }

/* Borders */
.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-t { border-top-width: 1px; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-accent { border-color: var(--accent); }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.left-0 { left: 0px; }
.mt-2 { margin-top: 0.5rem; }
.z-10 { z-index: 10; }

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Spacing utilities */
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 2rem;
}

/* Text utilities */
.leading-loose { line-height: 2; }
.tracking-wider { letter-spacing: 0.05em; }
.underline { text-decoration-line: underline; }

/* Transitions */
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

/* Hover states */
.hover\:text-accent:hover { color: var(--accent); }
.hover\:underline:hover { text-decoration-line: underline; }
.hover\:bg-accent:hover { background-color: var(--accent); }
.hover\:bg-opacity-20:hover { background-color: rgba(255, 0, 0, 0.2); }
.hover\:border-gray-700:hover { border-color: #374151; }

/* Focus states */
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:border-accent:focus { border-color: var(--accent); }

/* Shadow */
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }

/* Responsive design */
@media (min-width: 768px) {
  .md\:w-24 { width: 6rem; }
  .md\:w-32 { width: 8rem; }
  .md\:w-40 { width: 10rem; }
  .md\:py-16 { padding-top: 4rem; padding-bottom: 4rem; }
  .md\:py-24 { padding-top: 6rem; padding-bottom: 6rem; }
  .md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .md\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:gap-12 { gap: 3rem; }
  .md\:flex-row { flex-direction: row; }
}

/* Content-specific styles */
.poem-content, .letter-content, .story-content {
  white-space: pre-wrap;
}

.poem-content {
  line-height: 1.5;
}

.letter-content {
  line-height: 1.5;
}

.story-content {
  line-height: 1.7;
}

.poem-content p, .letter-content p {
  margin-bottom: 0.7em;
}

.story-content p {
  margin-bottom: 1em;
}

.story-content h2 {
  margin-top: 2em;
  margin-bottom: 1em;
  font-size: 1.5rem;
}

.story-content section {
  margin-bottom: 2em;
}
