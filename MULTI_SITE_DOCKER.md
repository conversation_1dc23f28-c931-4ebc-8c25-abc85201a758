# Multi-Site Server with Docker Containers

## Current Setup
- System-wide nginx directly serves multiple websites
- Each website is in its own directory under /var/www/
- Sites currently configured:
  - sexandloveletters.com
  - outeniquastudios.com (already dockerized)

## Recommended Architecture
1. **System nginx**: Acts as a reverse proxy
2. **Docker containers**: One for each website
3. **Port mapping**: Each container uses a different port

## Implementation Steps for sexandloveletters.com

### 1. Start the Docker Container
```bash
cd /var/www/sexandloveletters.com
docker-compose up -d
```

### 2. Update nginx Configuration
Update `/etc/nginx/sites-available/sexandloveletters.com` to:

```nginx
server {
    listen 80;
    server_name sexandloveletters.com www.sexandloveletters.com;

    location / {
        proxy_pass http://localhost:8070;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. Reload nginx
```bash
sudo systemctl reload nginx
```

### 4. Test the Site
Visit https://sexandloveletters.com and ensure everything is working correctly.

## Benefits of This Approach
1. **Isolation**: Each site runs in its own container
2. **Independence**: Updates to one site won't affect others
3. **Security**: Better isolation between sites
4. **Portability**: Easy to move sites between servers
5. **Scalability**: Can scale each site independently

## Managing Updates
When you need to update content:
1. Update the files in `/var/www/sexandloveletters.com`
2. Rebuild and restart the Docker container:
```bash
cd /var/www/sexandloveletters.com
docker-compose down
docker-compose up -d --build
```

## Monitoring
Check the status of your containers:
```bash
docker ps
```

## Troubleshooting
If issues occur:
1. Check container logs: `docker-compose logs`
2. Check nginx logs: `sudo tail /var/log/nginx/error.log`
