#!/bin/bash
# Backup script for sexandloveletters.com before transitioning to Docker

# Create backup directory
BACKUP_DIR="/var/www/backups/sexandloveletters_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# Backup the entire site
echo "Backing up sexandloveletters.com to $BACKUP_DIR..."
cp -R /var/www/sexandloveletters.com $BACKUP_DIR

echo "Backup completed successfully."
echo "Backup location: $BACKUP_DIR"
