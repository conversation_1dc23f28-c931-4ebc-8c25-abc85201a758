#!/bin/bash
# Script to ensure the sexandloveletters container is running after reboot
# Place this in /etc/cron.d/reboot or add to @reboot cron job

# Wait for network to be available
sleep 30

# Change to the website directory
cd /var/www/sexandloveletters.com

# Check if container is running, start if not
if ! docker ps | grep -q sexandloveletters; then
    echo "[$(date)] Container not running, starting it now" >> /var/log/sexandloveletters-startup.log
    /usr/local/bin/docker-compose up -d >> /var/log/sexandloveletters-startup.log 2>&1
else
    echo "[$(date)] Container already running" >> /var/log/sexandloveletters-startup.log
fi
