const fs = require('fs');
const path = require('path');
const marked = require('marked');

// Create necessary directories if they don't exist
if (!fs.existsSync('./poems')) {
  fs.mkdirSync('./poems');
}

if (!fs.existsSync('./letters')) {
  fs.mkdirSync('./letters');
}

if (!fs.existsSync('./stories')) {
  fs.mkdirSync('./stories');
}

// Read content directory
const contentDir = './content';
const contentFiles = fs.readdirSync(contentDir);

// Separate poems and letters
const poems = [];
const letterFiles = ['Little Gecko.md', 'Mei-mei.md'];
const storyFiles = ['umqombothi.md'];

// Identify poems (files that are not letters or stories)
contentFiles.forEach(file => {
  if (!letterFiles.includes(file) && !storyFiles.includes(file) && file.endsWith('.md')) {
    poems.push(file);
  }
});

// Special file name mappings
const fileNameMappings = {
  "reckoning .md": "reckoning_.html",
  "what we don't say.md": "what we don_t say.html"
};

console.log(`Found ${poems.length} poems, ${letterFiles.length} letters, and ${storyFiles.length} stories`);

// Create a reusable header template function
function createHeaderTemplate(title, type, isSubpage = true) {
  const basePath = isSubpage ? '../' : '';
  const typeDescription = type === 'poem' 
    ? 'A poem from Sex & Love Letters exploring the depths of human connection and emotion.' 
    : type === 'letter' 
      ? 'A love letter exploring intimacy, desire, and vulnerability from Sex & Love Letters.' 
      : 'A short story from Sex & Love Letters exploring the complexities of human relationships and experience.';
  
  return `<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}${title ? ' | ' : ''}Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="${basePath}assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="${basePath}assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="${basePath}assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="${basePath}assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="${title ? 'article' : 'website'}">
    <meta property="og:url" content="https://sexandloveletters.com/${type ? `${type}s/` : ''}${title ? encodeURIComponent(title.replace(/\s+/g, '-').toLowerCase()) + '.html' : ''}">
    <meta property="og:title" content="${title ? title + ' | ' : ''}Sex & Love Letters">
    <meta property="og:description" content="${title ? typeDescription : 'A collection of poems, love letters, and short stories exploring the raw intersection of desire, intimacy, and emotional exposure.'}">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/${type ? `${type}s/` : ''}${title ? encodeURIComponent(title.replace(/\s+/g, '-').toLowerCase()) + '.html' : ''}">
    <meta property="twitter:title" content="${title ? title + ' | ' : ''}Sex & Love Letters">
    <meta property="twitter:description" content="${title ? typeDescription : 'A collection of poems, love letters, and short stories exploring the raw intersection of desire, intimacy, and emotional exposure.'}">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">`;
}

// Create a reusable navigation template function
function createNavTemplate(isSubpage = true) {
  const basePath = isSubpage ? '../' : '';
  
  return `
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="${basePath}index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="${basePath}collection.html" class="text-accent hover:underline">Collection</a>
            <a href="${basePath}about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="${basePath}assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute right-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="${basePath}feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="${basePath}feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="${basePath}feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="${basePath}feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>`;
}

// Create a reusable footer template function
function createFooterTemplate() {
  return `
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>`;
}

// Main HTML template function
function createHtmlTemplate(title, content, type, metadata = {}) {
  const { prevNext = {} } = metadata;
  const isSubpage = true;
  const basePath = isSubpage ? '../' : '';

  return `${createHeaderTemplate(title, type, isSubpage)}
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }

        .${type}-content {
            white-space: pre-wrap;
            line-height: ${type === 'story' ? '1.7' : '1.5'};
        }

        .${type}-content p {
            margin-bottom: ${type === 'story' ? '1em' : '0.7em'};
        }

        ${type === 'story' ? `.${type}-content h2 {
            margin-top: 2em;
            margin-bottom: 1em;
            font-size: 1.5rem;
        }

        .${type}-content section {
            margin-bottom: 2em;
        }` : ''}
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        ${createNavTemplate(isSubpage)}

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">${title}</h2>

            <div class="${type}-content my-8">
                ${content}
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=https://sexandloveletters.com/${type}s/${encodeURIComponent(title.replace(/\s+/g, '-').toLowerCase())}.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/${type}s/${encodeURIComponent(title.replace(/\s+/g, '-').toLowerCase())}.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                ${prevNext.prev ? 
                  `<a href="${prevNext.prev.url}" class="text-accent hover:underline">← ${prevNext.prev.title}</a>` : 
                  `<span></span>`}
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                ${prevNext.next ? 
                  `<a href="${prevNext.next.url}" class="text-accent hover:underline">${prevNext.next.title} →</a>` : 
                  `<span></span>`}
            </div>
        </div>
    </main>

    ${createFooterTemplate()}
</body>
</html>`;
}

// Process poems
const poemFiles = [];

// First, collect all the poem files and metadata
poems.forEach(file => {
  try {
    const filePath = path.join(contentDir, file);
    const content = fs.readFileSync(filePath, 'utf-8');      // Get filename without extension
      const title = path.basename(file, '.md');
      const sanitizedTitle = title.replace(/\s+/g, ' ');
      
      // Check if this file has a special mapping
      let htmlFileName;
      if (fileNameMappings[file]) {
        htmlFileName = fileNameMappings[file].replace(/\.html$/, '');
      } else {
        // Convert spaces to match your existing URL structure for HTML files
        htmlFileName = sanitizedTitle.toLowerCase().replace(/'/g, '').replace(/\./g, '').replace(/\(/g, '').replace(/\)/g, '').replace(/\s+/g, '-');
      }
    
    poemFiles.push({
      originalFile: file,
      title: title,
      htmlFileName: htmlFileName,
      url: `${htmlFileName}.html`  // Removed 'poems/' prefix
    });
  } catch (error) {
    console.error(`Error processing ${file}:`, error.message);
  }
});

// Sort poems alphabetically by title
poemFiles.sort((a, b) => a.title.localeCompare(b.title));

// Now process each poem with prev/next navigation
poemFiles.forEach((poem, index) => {
  try {
    const filePath = path.join(contentDir, poem.originalFile);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // Check for frontmatter (---)
    let cleanContent = content;
    if (content.startsWith('---')) {
      const frontmatterEnd = content.indexOf('---', 3);
      if (frontmatterEnd !== -1) {
        cleanContent = content.slice(frontmatterEnd + 3).trim();
      }
    } else {
      // Remove HTML comments and trim whitespace
      cleanContent = content.replace(/<!--.*?-->/gs, '').trim();
    }
    
    // Improve poem formatting by adding each line in a separate paragraph
    // This gives better control over spacing and line-height
    const lines = cleanContent.split(/\n+/);
    const htmlContent = lines.map(line => line.trim() ? `<p>${line}</p>` : '<br>').join('');
    
    // Calculate prev/next links
    const prevPoem = index > 0 ? poemFiles[index - 1] : null;
    const nextPoem = index < poemFiles.length - 1 ? poemFiles[index + 1] : null;
    
    const metadata = {
      prevNext: {
        prev: prevPoem ? { title: prevPoem.title, url: prevPoem.url } : null,
        next: nextPoem ? { title: nextPoem.title, url: nextPoem.url } : null
      }
    };
    
    // Create HTML file
    const htmlFilePath = path.join('./poems', `${poem.htmlFileName}.html`);
    fs.writeFileSync(htmlFilePath, createHtmlTemplate(poem.title, htmlContent, 'poem', metadata));
    
    console.log(`Generated poem: ${htmlFilePath}`);
  } catch (error) {
    console.error(`Error processing ${poem.originalFile}:`, error.message);
  }
});

// Process letters
const letterFilesInfo = [];

// First, collect all the letter files and metadata
letterFiles.forEach(file => {
  try {
    const filePath = path.join(contentDir, file);
    if (fs.existsSync(filePath)) {
      // Get filename without extension
      const title = path.basename(file, '.md');
      const sanitizedTitle = title.replace(/\s+/g, ' ');
      const htmlFileName = sanitizedTitle.toLowerCase().replace(/'/g, '').replace(/\s+/g, '-');
      
      letterFilesInfo.push({
        originalFile: file,
        title: title,
        htmlFileName: htmlFileName,
        url: `${htmlFileName}.html`  // Removed 'letters/' prefix
      });
    } else {
      console.error(`File not found: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error.message);
  }
});

// Sort letters alphabetically by title
letterFilesInfo.sort((a, b) => a.title.localeCompare(b.title));

// Now process each letter with prev/next navigation
letterFilesInfo.forEach((letter, index) => {
  try {
    const filePath = path.join(contentDir, letter.originalFile);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // Remove HTML comments and convert to HTML
    const cleanContent = content.replace(/<!--.*?-->/gs, '').trim();
    
    // Process the letter content in a way similar to poems
    // This will ensure consistent spacing and formatting
    const lines = cleanContent.split(/\n+/);
    const htmlContent = lines.map(line => line.trim() ? `<p>${line}</p>` : '<br>').join('');
    
    // Calculate prev/next links
    const prevLetter = index > 0 ? letterFilesInfo[index - 1] : null;
    const nextLetter = index < letterFilesInfo.length - 1 ? letterFilesInfo[index + 1] : null;
    
    const metadata = {
      prevNext: {
        prev: prevLetter ? { title: prevLetter.title, url: prevLetter.url } : null,
        next: nextLetter ? { title: nextLetter.title, url: nextLetter.url } : null
      }
    };
    
    // Create HTML file
    const htmlFilePath = path.join('./letters', `${letter.htmlFileName}.html`);
    fs.writeFileSync(htmlFilePath, createHtmlTemplate(letter.title, htmlContent, 'letter', metadata));
    
    console.log(`Generated letter: ${htmlFilePath}`);
  } catch (error) {
    console.error(`Error processing ${letter.originalFile}:`, error.message);
  }
});

console.log('Generation complete!');

// Process story files
const storyFilesInfo = [];

// Parse each story file
storyFiles.forEach(file => {
  try {
    const filePath = path.join(contentDir, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // Extract title from first line (assuming it's the title)
      const lines = content.split('\n');
      const title = lines[0].replace(/^#\s+/, '').trim();
      
      // Extract metadata if present
      // Look for Author: and Date: in the first few lines
      let author = null;
      let date = null;
      
      // Check the first 10 lines for metadata
      for (let i = 1; i < Math.min(10, lines.length); i++) {
        const line = lines[i].trim();
        if (line.startsWith('Author:')) {
          author = line.replace('Author:', '').trim();
        } else if (line.startsWith('Date:')) {
          date = line.replace('Date:', '').trim();
        }
      }
      
      // Generate HTML file name based on title
      let htmlFileName = file.replace(/\.md$/, '');
      if (fileNameMappings[file]) {
        htmlFileName = fileNameMappings[file].replace(/\.html$/, '');
      }
      
      storyFilesInfo.push({
        originalFile: file,
        title: title,
        htmlFileName: htmlFileName,
        url: `${htmlFileName}.html`,
        author: author,
        date: date
      });
    } else {
      console.error(`File not found: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error.message);
  }
});

// Sort stories alphabetically by title
storyFilesInfo.sort((a, b) => a.title.localeCompare(b.title));

// Now process each story with prev/next navigation
storyFilesInfo.forEach((story, index) => {
  try {
    const filePath = path.join(contentDir, story.originalFile);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // Remove HTML comments and convert to HTML
    const cleanContent = content.replace(/<!--.*?-->/gs, '').trim();
    
    // Process the story content
    const lines = cleanContent.split('\n');
    let htmlContent = '';
    let inSection = false;
    let currentSection = '';
    let paragraphBuffer = [];
    let wordCount = 0;
    let skipLines = 1; // Skip title line
    
    // Skip metadata lines
    for (let i = 1; i < Math.min(10, lines.length); i++) {
      const line = lines[i].trim();
      if (line.startsWith('Author:') || line.startsWith('Date:') || line.trim() === '') {
        skipLines++;
      } else {
        break;
      }
    }
    
    lines.forEach((line, i) => {
      if (i < skipLines) return; // Skip title and metadata lines
      
      // Count words for read time estimation
      if (line.trim() !== '' && !line.match(/^\d+\.\s+/)) {
        wordCount += line.trim().split(/\s+/).length;
      }
      
      // Check if this is a section header (numbered sections)
      if (line.match(/^\d+\.\s+/)) {
        // Flush paragraph buffer before starting new section
        if (paragraphBuffer.length > 0) {
          htmlContent += `<p>${paragraphBuffer.join(' ')}</p>\n`;
          paragraphBuffer = [];
        }
        
        if (inSection) {
          htmlContent += `</section>`;
        }
        const sectionTitle = line.trim();
        htmlContent += `<section class="mb-8">\n<h2>${sectionTitle}</h2>\n`; // Add section wrapper
        inSection = true;
        currentSection = sectionTitle;
      } else if (line.trim() !== '') {
        // Add to paragraph buffer
        paragraphBuffer.push(line.trim());
      } else if (line.trim() === '') {
        // Empty line means paragraph break
        if (paragraphBuffer.length > 0) {
          htmlContent += `<p>${paragraphBuffer.join(' ')}</p>\n`;
          paragraphBuffer = [];
        }
      }
    });
    
    // Flush any remaining paragraph content
    if (paragraphBuffer.length > 0) {
      htmlContent += `<p>${paragraphBuffer.join(' ')}</p>\n`;
    }
    
    if (inSection) {
      htmlContent += `</section>`;
    }
    
    // Calculate read time (average reading speed: 200-250 words per minute)
    const readTimeMinutes = Math.max(1, Math.ceil(wordCount / 225));
    const readTimeText = `${readTimeMinutes} min read`;
    
    // Calculate prev/next links
    const prevStory = index > 0 ? storyFilesInfo[index - 1] : null;
    const nextStory = index < storyFilesInfo.length - 1 ? storyFilesInfo[index + 1] : null;
    
    const metadata = {
      prevNext: {
        prev: prevStory ? { title: prevStory.title, url: prevStory.url } : null,
        next: nextStory ? { title: nextStory.title, url: nextStory.url } : null
      },
      storyMetadata: {
        author: story.author,
        date: story.date,
        readTime: readTimeText
      }
    };
    
    // Create HTML file
    const htmlFilePath = path.join('./stories', `${story.htmlFileName}.html`);
    fs.writeFileSync(htmlFilePath, createHtmlTemplate(story.title, htmlContent, 'story', metadata));
    
    console.log(`Generated story: ${htmlFilePath}`);
  } catch (error) {
    console.error(`Error processing ${story.originalFile}:`, error.message);
  }
});

// Update collection.html with all poem and letter links
function updateCollectionHTML() {
  console.log('Updating collection.html...');
  
  try {
    // Read the collection.html file
    const collectionPath = './collection.html';
    let collectionHTML = fs.readFileSync(collectionPath, 'utf-8');
    
    // Generate poem links HTML
    const sortedPoemTitles = poemFiles.map(p => p.title).sort((a, b) => a.localeCompare(b));
    const poemLinksHTML = sortedPoemTitles.map(title => {
      const poem = poemFiles.find(p => p.title === title);
      return `                <a href="poems/${poem.htmlFileName}.html" class="hover:text-accent transition-colors">${title}</a>`;
    }).join('\n');
    
    // Generate letter links HTML
    const sortedLetterTitles = letterFilesInfo.map(l => l.title).sort((a, b) => a.localeCompare(b));
    const letterLinksHTML = sortedLetterTitles.map(title => {
      const letter = letterFilesInfo.find(l => l.title === title);
      return `                    <a href="letters/${letter.htmlFileName}.html" class="hover:text-accent transition-colors">${title}</a>`;
    }).join('\n');
    
    // Generate story links HTML
    const sortedStoryTitles = storyFilesInfo.map(s => s.title).sort((a, b) => a.localeCompare(b));
    const storyLinksHTML = sortedStoryTitles.map(title => {
      const story = storyFilesInfo.find(s => s.title === title);
      return `                    <a href="stories/${story.htmlFileName}.html" class="hover:text-accent transition-colors">${title}</a>`;
    }).join('\n');
    
    // Update poems section in collection.html
    const poemsStartMarker = '<div class="poems-grid grid grid-cols-1 md:grid-cols-2 gap-4 mb-16">';
    const poemsEndMarker = '</div>\n            </div>';
    const poemsSectionRegex = new RegExp(`${poemsStartMarker}[\\s\\S]*?${poemsEndMarker}`);
    
    collectionHTML = collectionHTML.replace(poemsSectionRegex, `${poemsStartMarker}\n${poemLinksHTML}\n            ${poemsEndMarker}`);
    
    // Update letters section in collection.html
    const lettersStartMarker = '<div class="letters-grid grid grid-cols-1 md:grid-cols-2 gap-4">';
    const lettersEndMarker = '</div>\n            </div>';
    const lettersSectionRegex = new RegExp(`${lettersStartMarker}[\\s\\S]*?${lettersEndMarker}`);
    
    collectionHTML = collectionHTML.replace(lettersSectionRegex, `${lettersStartMarker}\n${letterLinksHTML}\n                ${lettersEndMarker}`);
    
    // Update stories section in collection.html
    const storiesStartMarker = '<div class="stories-grid grid grid-cols-1 md:grid-cols-2 gap-4">';
    const storiesEndMarker = '</div>\n            </div>';
    const storiesSectionRegex = new RegExp(`${storiesStartMarker}[\\s\\S]*?${storiesEndMarker}`);
    
    collectionHTML = collectionHTML.replace(storiesSectionRegex, `${storiesStartMarker}\n${storyLinksHTML}\n                ${storiesEndMarker}`);
    
    // Write updated collection.html
    fs.writeFileSync(collectionPath, collectionHTML);
    console.log('collection.html updated successfully!');
  } catch (error) {
    console.error('Error updating collection.html:', error.message);
  }
}

// Call the function to update collection.html
updateCollectionHTML();

// Generate stories index.html
function generateStoriesIndex() {
  console.log('Generating stories/index.html...');
  
  try {
    // Generate HTML for story cards
    const storyCardsHTML = storyFilesInfo.map(story => {
      // Create a preview from the story content (first 150 characters)
      const filePath = path.join(contentDir, story.originalFile);
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');
      
      // Skip title and any metadata lines at the beginning
      let previewText = '';
      let skipLines = 1; // Skip title line
      
      // Skip metadata lines
      for (let i = 1; i < Math.min(10, lines.length); i++) {
        const line = lines[i].trim();
        if (line.startsWith('Author:') || line.startsWith('Date:') || line.trim() === '') {
          skipLines++;
        } else {
          break;
        }
      }
      
      // Get content for preview
      for (let i = skipLines; i < lines.length; i++) {
        if (lines[i].trim() !== '') {
          previewText += ' ' + lines[i].trim();
          if (previewText.length > 200) break;
        }
      }
      
      // Count words for read time
      let wordCount = 0;
      for (let i = skipLines; i < lines.length; i++) {
        if (lines[i].trim() !== '' && !lines[i].match(/^\d+\.\s+/)) {
          wordCount += lines[i].trim().split(/\s+/).length;
        }
      }
      const readTimeMinutes = Math.max(1, Math.ceil(wordCount / 225));
      const readTimeText = `${readTimeMinutes} min read`;
      
      // Trim and add ellipsis if needed
      previewText = previewText.trim();
      if (previewText.length > 150) {
        previewText = previewText.substring(0, 150) + '...';
      }
      
      return `
            <article class="border border-gray-800 p-6 rounded-lg hover:border-gray-700 transition-colors">
                <h2 class="text-xl md:text-2xl mb-3">
                    <a href="${story.htmlFileName}.html" class="hover:text-accent transition-colors">${story.title}</a>
                </h2>
                <p class="text-gray-300 mb-4">${previewText}</p>
                <div class="text-sm text-gray-500">
                    ${story.author ? `<span class="mr-3">By ${story.author}</span>` : ''}
                    ${story.date ? `<time class="mr-3" datetime="${story.date}">${story.date}</time>` : ''}
                    <span>${readTimeText}</span>
                </div>
            </article>`;
    }).join('\n');
    
    // Create stories index.html
    const storiesIndexHTML = `<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Short Stories | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexandloveletters.com/stories/index.html">
    <meta property="og:title" content="Short Stories | Sex & Love Letters">
    <meta property="og:description" content="A collection of short stories exploring the depths of human connection, culture, and relationships.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/stories/index.html">
    <meta property="twitter:title" content="Short Stories | Sex & Love Letters">
    <meta property="twitter:description" content="A collection of short stories exploring the depths of human connection, culture, and relationships.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <script src="https://cdn.tailwindcss.com/3.4.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-16">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-20 md:w-24 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-8">Short Stories</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow">
        <nav class="flex justify-center gap-6 md:gap-12 text-lg mb-12">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="hover:text-accent transition-colors">Collection</a>
            <a href="index.html" class="text-accent hover:underline">Stories</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
        </nav>

        <section class="mb-10 text-center max-w-2xl mx-auto">
            <p class="text-lg text-gray-300">Welcome to our collection of short stories. Each narrative explores the complex interplay of relationships, culture, and human connection.</p>
        </section>

        <section class="grid gap-12 mb-16">
            ${storyCardsHTML}
            
            <!-- More stories will be added here -->
        </section>
    </main>

    <footer class="w-full max-w-3xl py-8 text-center text-gray-500 text-sm">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>`;
    
    // Write the file
    fs.writeFileSync('./stories/index.html', storiesIndexHTML);
    console.log('Generated stories/index.html');
  } catch (error) {
    console.error('Error generating stories index:', error.message);
  }
}

// Call the function to generate stories index
generateStoriesIndex();
