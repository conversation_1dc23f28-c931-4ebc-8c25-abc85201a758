<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Share Test - Sex & Love Letters</title>
    
    <!-- Current social sharing setup -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexandloveletters.com/social-share-test.html">
    <meta property="og:title" content="Social Share Test - Sex & Love Letters">
    <meta property="og:description" content="Testing social sharing images for Sex & Love Letters">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-whatsapp-v2.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/social-share-test.html">
    <meta property="twitter:title" content="Social Share Test - Sex & Love Letters">
    <meta property="twitter:description" content="Testing social sharing images for Sex & Love Letters">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-whatsapp-v2.png">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .image-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .image-preview img {
            display: block;
            margin: 0 auto 5px;
            border: 1px solid #ddd;
            max-width: 200px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .share-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .share-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Social Share Test Page</h1>
    <p>This page helps you test social sharing images and metadata.</p>
    
    <div class="test-section">
        <h2>Current Social Share Image</h2>
        <p class="info">Now using: <strong>cicada-whatsapp-v2.png</strong> (1200x1200)</p>
        <div class="image-preview">
            <img src="assets/cicada-whatsapp-v2.png" alt="Current Social Share Image">
            <small>cicada-whatsapp-v2.png</small>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Available Social Images</h2>
        <div class="image-preview">
            <img src="assets/cicada.png" alt="Original Cicada">
            <small>cicada.png (1024x1024)</small>
        </div>
        <div class="image-preview">
            <img src="assets/cicada-social.png" alt="Social Cicada">
            <small>cicada-social.png (1200x1200)</small>
        </div>
        <div class="image-preview">
            <img src="assets/cicada-whatsapp.png" alt="WhatsApp Cicada">
            <small>cicada-whatsapp.png (1200x1200)</small>
        </div>
        <div class="image-preview">
            <img src="assets/cicada-whatsapp-v2.png" alt="WhatsApp Cicada v2">
            <small>cicada-whatsapp-v2.png (1200x1200) ✓ Current</small>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test Social Sharing</h2>
        <p>Click these buttons to test how the page appears when shared:</p>
        
        <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/social-share-test.html" 
           class="share-button" target="_blank" rel="noopener">
           Test Facebook Share
        </a>
        
        <a href="https://twitter.com/intent/tweet?url=https://sexandloveletters.com/social-share-test.html&text=Testing social sharing for Sex & Love Letters" 
           class="share-button" target="_blank" rel="noopener">
           Test Twitter Share
        </a>
        
        <a href="https://www.linkedin.com/sharing/share-offsite/?url=https://sexandloveletters.com/social-share-test.html" 
           class="share-button" target="_blank" rel="noopener">
           Test LinkedIn Share
        </a>
    </div>
    
    <div class="test-section">
        <h2>Social Media Debugging Tools</h2>
        <p>Use these tools to check how your pages appear on social media:</p>
        <ul>
            <li><a href="https://developers.facebook.com/tools/debug/" target="_blank">Facebook Sharing Debugger</a></li>
            <li><a href="https://cards-dev.twitter.com/validator" target="_blank">Twitter Card Validator</a></li>
            <li><a href="https://www.linkedin.com/post-inspector/" target="_blank">LinkedIn Post Inspector</a></li>
        </ul>
        <p class="info">Enter your page URLs in these tools to see how they'll appear when shared.</p>
    </div>
    
    <div class="test-section">
        <h2>What Changed</h2>
        <ul class="success">
            <li>✓ Updated all social sharing images to use <strong>cicada-whatsapp-v2.png</strong></li>
            <li>✓ Added proper image dimensions (1200x1200) for optimal display</li>
            <li>✓ Added image alt text for accessibility</li>
            <li>✓ Applied changes to all poems, letters, and stories</li>
            <li>✓ Consistent social sharing across the entire site</li>
        </ul>
    </div>
    
    <p><a href="index.html">← Back to main site</a></p>
    
    <script>
        // Log social meta tags for debugging
        window.addEventListener('load', function() {
            const ogImage = document.querySelector('meta[property="og:image"]');
            const twitterImage = document.querySelector('meta[property="twitter:image"]');
            
            console.log('Open Graph Image:', ogImage ? ogImage.content : 'Not found');
            console.log('Twitter Image:', twitterImage ? twitterImage.content : 'Not found');
        });
    </script>
</body>
</html>
