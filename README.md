# Sex & Love Letters

A collection of poems, love letters, and short stories exploring the intersection of desire, vulnerability, and healing.

## Site Structure

- `index.html` - Home page
- `collection.html` - Collection of all poems, letters, and stories with search and filtering
- `about.html` - About page
- `content/` - Markdown files for all poems, letters, and stories
- `poems/` - Generated HTML files for poems
- `letters/` - Generated HTML files for letters
- `stories/` - Generated HTML files for stories
- `feeds/` - RSS feed XML files
- `assets/` - Images and other assets

## Features

- **Static Site Generation**: Converts markdown files to HTML
- **Search Functionality**: Search poems and letters by title
- **Content Filtering**: Filter by content type (poems/letters/stories)
- **Navigation**: Previous/next navigation between poems and letters
- **Responsive Design**: Works on mobile and desktop devices
- **Social Sharing**: Twitter and Facebook sharing for all content
- **RSS Feeds**: RSS feeds for all content types
- **Automated Workflow**: Automatically generates HTML files when new markdown content is added

## Development

### Local Development

To generate HTML files from the markdown content locally:

```bash
# Install dependencies
npm install

# Generate HTML files
npm run generate

# Watch for changes to markdown files and automatically regenerate
npm run watch
```

### Automated Workflow

This project uses GitHub Actions to automatically generate HTML files when new markdown content is added to the `content` directory. The workflow:

1. Triggers when markdown files are pushed to the `content` directory
2. Installs dependencies
3. Runs the generate script to convert markdown to HTML
4. Updates the collection.html file with links to the new content
5. Commits and pushes the generated files back to the repository

This means you only need to:
1. Add your markdown files to the `content` directory
2. Commit and push them to GitHub
3. The GitHub Action will handle the rest!

## How to Add New Content

1. Create a new markdown file in the `content/` directory:
   - For poems, simply create a file with the poem's title as the filename (e.g., `your poem title.md`)
   - For letters, create a file with the letter's title as the filename and add it to the `letterFiles` array in `generate.js`
   - For stories, create a file with the story's title as the filename and add it to the `storyFiles` array in both `generate.js` and `generate-stories.js`

2. Run the generator to create HTML files:
   ```
   npm run generate
   ```

3. Generate RSS feeds:
   ```
   node generate-rss.js
   ```

4. Or use the regenerate script to update everything at once:
   ```
   ./regenerate_content.sh
   ```

## Customizing the Design

The HTML templates are defined in the `generate.js` file. To modify the design:

1. Edit the `createHtmlTemplate` function in `generate.js`
2. Run the generator to update all HTML files

## Technologies Used

- HTML
- Tailwind CSS
- Node.js for static site generation
- Markdown for content
- RSS 2.0 specification for feeds

## RSS Implementation

The site provides RSS feeds for various content types:

- `feeds/main.xml` - All content (poems, letters, and stories)
- `feeds/poems.xml` - Poems only
- `feeds/letters.xml` - Letters only
- `feeds/stories.xml` - Stories only

Each feed includes:
- Content title
- Link to the content page
- Publication date
- Content preview
- Atom self-reference link

The RSS functionality is implemented in `generate-rss.js` which reads the content files and generates the appropriate XML files. RSS links are included in the site navigation and are visible on all pages through the Subscribe dropdown menu in the navigation bar.

## License

All Rights Reserved &copy; 2025
