#!/bin/bash
# This script updates all poem, letter, and story pages to include the RSS dropdown menu

# Define the base directory
BASE_DIR="/var/www/sexandloveletters.com"

# Function to add RSS dropdown to an HTML file
add_rss_dropdown() {
  local file="$1"
  local rel_path="$2"
  
  # Find the closing nav tag
  if grep -q '<nav class="flex gap-6 md:gap-12 text-lg mb-16">' "$file"; then
    # Replace the navigation section with the updated one including the dropdown
    sed -i '/<nav class="flex gap-6 md:gap-12 text-lg mb-16">/,/<\/nav>/c\
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">\
            <a href="'"$rel_path"'index.html" class="hover:text-accent transition-colors">Home<\/a>\
            <a href="'"$rel_path"'collection.html" class="text-accent hover:underline">Collection<\/a>\
            <a href="'"$rel_path"'about.html" class="hover:text-accent transition-colors">About<\/a>\
            <div class="relative group">\
                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true" id="subscribeButton" onclick="toggleDropdown(event)">\
                    <img src="'"$rel_path"'assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">\
                    <span>Subscribe<\/span>\
                <\/a>\
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">\
                    <a href="'"$rel_path"'feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content<\/a>\
                    <a href="'"$rel_path"'feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems<\/a>\
                    <a href="'"$rel_path"'feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters<\/a>\
                    <a href="'"$rel_path"'feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories<\/a>\
                <\/div>\
            <\/div>\
        <\/nav>' "$file"
  fi
  
  # Check if the JavaScript toggle function is already added
  if ! grep -q "function toggleDropdown" "$file"; then
    # Add the JavaScript toggle function before the closing body tag
    sed -i '/<\/body>/i\
    <script>\
        // Function to toggle the dropdown menu\
        function toggleDropdown(event) {\
            event.preventDefault();\
            const dropdown = document.getElementById("subscribeDropdown");\
            dropdown.classList.toggle("hidden");\
            \
            // Close dropdown when clicking outside\
            document.addEventListener("click", function closeDropdown(e) {\
                const subscribeButton = document.getElementById("subscribeButton");\
                if (e.target !== subscribeButton && !subscribeButton.contains(e.target) && !dropdown.contains(e.target)) {\
                    dropdown.classList.add("hidden");\
                    document.removeEventListener("click", closeDropdown);\
                }\
            });\
        }\
    <\/script>' "$file"
  fi
}

# Update poem pages
echo "Updating poem pages..."
for poem_file in "$BASE_DIR/poems"/*.html; do
  add_rss_dropdown "$poem_file" "../"
  echo "Updated $poem_file"
done

# Update letter pages
echo "Updating letter pages..."
for letter_file in "$BASE_DIR/letters"/*.html; do
  add_rss_dropdown "$letter_file" "../"
  echo "Updated $letter_file"
done

# Update story pages
echo "Updating story pages..."
for story_file in "$BASE_DIR/stories"/*.html; do
  # Skip the index.html file in the stories directory
  if [[ $(basename "$story_file") != "index.html" ]]; then
    add_rss_dropdown "$story_file" "../"
    echo "Updated $story_file"
  fi
done

echo "All updates completed!"
