#!/bin/bash
# Script to create optimized social sharing images with better contrast

echo "Creating optimized social sharing images..."

# Check if ImageMagick is available
if ! command -v convert &> /dev/null; then
    echo "ImageMagick not found. Please install it to create optimized social images."
    echo "On Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "On macOS: brew install imagemagick"
    exit 1
fi

# Create a version with white background and border for better visibility
echo "Creating cicada-social-light.png with light background..."
convert assets/cicada.png \
    -resize 1200x1200 \
    -background white \
    -gravity center \
    -extent 1200x1200 \
    -bordercolor "#f0f0f0" \
    -border 50x50 \
    assets/cicada-social-light.png

# Create a version with subtle shadow for better contrast
echo "Creating cicada-social-shadow.png with drop shadow..."
convert assets/cicada.png \
    -resize 1100x1100 \
    \( +clone -background black -shadow 60x5+5+5 \) \
    +swap -background white -layers merge +repage \
    -gravity center \
    -extent 1200x1200 \
    assets/cicada-social-shadow.png

# Create a version with colored background matching your brand
echo "Creating cicada-social-branded.png with brand colors..."
convert assets/cicada.png \
    -resize 1000x1000 \
    -background "#1a1a1a" \
    -gravity center \
    -extent 1200x1200 \
    -bordercolor "#ff0000" \
    -border 10x10 \
    assets/cicada-social-branded.png

echo "Created social sharing image variants:"
echo "- cicada-social-light.png (white background)"
echo "- cicada-social-shadow.png (with drop shadow)"
echo "- cicada-social-branded.png (dark background with red border)"
echo ""
echo "Test these images and update your social sharing to use the best one:"
echo "1. Update generate.js to use the preferred image"
echo "2. Update static HTML files (index.html, collection.html, about.html)"
echo "3. Run ./regenerate_all.sh to apply changes"
