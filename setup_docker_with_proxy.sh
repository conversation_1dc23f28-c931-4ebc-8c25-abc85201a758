#!/bin/bash
# Script to set up Docker container with nginx reverse proxy

echo "Setting up sexandloveletters.com Docker container with nginx reverse proxy..."

# Step 1: Start Docker container
cd /var/www/sexandloveletters.com
docker-compose up -d

# Check if Docker container started successfully
if [ $? -ne 0 ]; then
    echo "Error: Failed to start Docker container!"
    exit 1
fi

# Step 2: Create backup of current nginx config
BACKUP_FILE="/etc/nginx/sites-available/sexandloveletters.com.bak.$(date +%Y%m%d%H%M%S)"
sudo cp /etc/nginx/sites-available/sexandloveletters.com "$BACKUP_FILE"
echo "Current nginx config backed up to: $BACKUP_FILE"

# Step 3: Update nginx config to use reverse proxy
sudo cp /var/www/sexandloveletters.com/nginx-proxy.conf /etc/nginx/sites-available/sexandloveletters.com

# Step 4: Test nginx config
echo "Testing nginx configuration..."
sudo nginx -t

if [ $? -ne 0 ]; then
    echo "Error: nginx configuration test failed!"
    echo "Restoring previous nginx config..."
    sudo cp "$BACKUP_FILE" /etc/nginx/sites-available/sexandloveletters.com
    sudo systemctl reload nginx
    exit 1
fi

# Step 5: Reload nginx
echo "Reloading nginx..."
sudo systemctl reload nginx

echo "Setup completed successfully!"
echo "Your website should now be served from the Docker container via nginx reverse proxy."
echo ""
echo "To check the status of your Docker container:"
echo "    docker ps | grep sexandloveletters"
echo ""
echo "To view Docker container logs:"
echo "    cd /var/www/sexandloveletters.com && docker-compose logs -f"
echo ""
echo "If you need to revert to the previous setup:"
echo "    sudo cp $BACKUP_FILE /etc/nginx/sites-available/sexandloveletters.com"
echo "    sudo systemctl reload nginx"
