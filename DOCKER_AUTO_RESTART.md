# Docker Auto-Restart Configuration

This document explains how the Docker container for sexandloveletters.com is configured to automatically restart after server reboots.

## Docker Compose Restart Policy

The container is configured with `restart: unless-stopped` in the docker-compose.yml file:

```yaml
services:
  sexandloveletters:
    # ... other configuration ...
    restart: unless-stopped
```

This means:
- The container will automatically start when the Docker daemon starts (which happens at system boot)
- If the container crashes, it will be restarted automatically
- The only time it won't restart is if you manually stop it using `docker-compose down` or similar commands

## Additional Cron Job

For extra reliability, a cron job has been set up to ensure the container starts even if there are issues with Dock<PERSON>'s auto-restart:

```bash
# The script at /var/www/sexandloveletters.com/ensure_running.sh is run at system startup
@reboot /var/www/sexandloveletters.com/ensure_running.sh
```

You can check if the cron job is installed with:
```bash
crontab -l | grep ensure_running
```

## Verifying Auto-Restart

To verify that auto-restart is working after a reboot:

1. Reboot your server:
   ```bash
   sudo reboot
   ```

2. After the server comes back online, check if the container is running:
   ```bash
   cd /var/www/sexandloveletters.com
   docker-compose ps
   ```

3. You should see your container in the "Up" state.

4. You can also check the startup log:
   ```bash
   cat /var/log/sexandloveletters-startup.log
   ```

## Manual Start (If Needed)

If for some reason the container doesn't start automatically, you can start it manually:

```bash
cd /var/www/sexandloveletters.com
docker-compose up -d
```
