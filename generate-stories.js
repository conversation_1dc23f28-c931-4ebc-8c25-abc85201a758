// Generate Stories Script
const fs = require('fs');
const path = require('path');

// Import the template functions from generate.js
const { createHeaderTemplate, createNavTemplate, createFooterTemplate } = require('./generate');

// Create stories directory if it doesn't exist
if (!fs.existsSync('./stories')) {
  fs.mkdirSync('./stories');
}

// Read content directory
const contentDir = './content';
const storyFiles = ['umqombothi.md'];

// Story-specific HTML template function
function createStoryHtmlTemplate(title, content, metadata = {}) {
  const { prevNext = {} } = metadata;
  const type = 'story';
  const isSubpage = true;
  
  return `${createHeaderTemplate(title, type, isSubpage)}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
        
        .story-content {
            white-space: pre-wrap;
            line-height: 1.7;
        }
        
        .story-content h2 {
            margin-top: 2em;
            margin-bottom: 1em;
            font-size: 1.5rem;
        }
        
        .story-content p {
            margin-bottom: 1em;
        }
        
        .story-content section {
            margin-bottom: 2em;
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        ${createNavTemplate(isSubpage)}

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">${title}</h2>

            <div class="story-content my-8">
                ${content}
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=https://sexandloveletters.com/stories/${encodeURIComponent(title.replace(/\s+/g, '-').toLowerCase())}.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/stories/${encodeURIComponent(title.replace(/\s+/g, '-').toLowerCase())}.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                ${prevNext.prev ? 
                  `<a href="${prevNext.prev.url}" class="text-accent hover:underline">← ${prevNext.prev.title}</a>` : 
                  `<span></span>`}
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                ${prevNext.next ? 
                  `<a href="${prevNext.next.url}" class="text-accent hover:underline">${prevNext.next.title} →</a>` : 
                  `<span></span>`}
            </div>
        </div>
    </main>

    ${createFooterTemplate()}
</body>
</html>`;
}

// Process story files
const storyFilesInfo = [];

// Parse each story file
storyFiles.forEach(file => {
  try {
    const filePath = path.join(contentDir, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // Extract title from first line (assuming it's the title)
      const lines = content.split('\n');
      const title = lines[0].replace(/^#\s+/, '').trim();
      
      // Extract metadata if present
      let author = null;
      let date = null;
      let skipLines = 1; // Skip title line
      
      // Check the first 10 lines for metadata
      for (let i = 1; i < Math.min(10, lines.length); i++) {
        const line = lines[i].trim();
        if (line.startsWith('Author:')) {
          author = line.replace('Author:', '').trim();
          skipLines++;
        } else if (line.startsWith('Date:')) {
          date = line.replace('Date:', '').trim();
          skipLines++;
        } else if (line.trim() === '') {
          skipLines++;
        } else {
          break;
        }
      }
      
      // Extract an excerpt for the index page
      let excerpt = '';
      for (let i = skipLines; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line !== '' && !line.match(/^\d+\.\s+/)) {
          excerpt += ' ' + line;
          if (excerpt.length > 200) break;
        }
      }
      
      // Truncate excerpt if too long
      if (excerpt.length > 200) {
        excerpt = excerpt.trim().substring(0, 197) + '...';
      } else if (excerpt.length > 0) {
        excerpt = excerpt.trim() + '...';
      }
      
      // Generate HTML file name based on title
      let htmlFileName = file.replace(/\.md$/, '');
      
      storyFilesInfo.push({
        originalFile: file,
        title: title,
        htmlFileName: htmlFileName,
        url: `${htmlFileName}.html`,
        author: author,
        date: date,
        excerpt: excerpt
      });
    } else {
      console.error(`File not found: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error.message);
  }
});

// Sort stories alphabetically by title
storyFilesInfo.sort((a, b) => a.title.localeCompare(b.title));

// Now process each story
storyFilesInfo.forEach((story, index) => {
  try {
    const filePath = path.join(contentDir, story.originalFile);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // Remove HTML comments and convert to HTML
    const cleanContent = content.replace(/<!--.*?-->/gs, '').trim();
    
    // Process the story content
    const lines = cleanContent.split('\n');
    let htmlContent = '';
    let inSection = false;
    let currentSection = '';
    let paragraphBuffer = [];
    let wordCount = 0;
    let skipLines = 1; // Skip title line
    
    // Skip metadata lines
    for (let i = 1; i < Math.min(10, lines.length); i++) {
      const line = lines[i].trim();
      if (line.startsWith('Author:') || line.startsWith('Date:') || line.trim() === '') {
        skipLines++;
      } else {
        break;
      }
    }
    
    // Process content lines
    for (let i = 0; i < lines.length; i++) {
      if (i < skipLines) continue; // Skip title and metadata
      
      const line = lines[i];
      
      // Count words for read time estimation
      if (line.trim() !== '' && !line.match(/^\d+\.\s+/)) {
        wordCount += line.trim().split(/\s+/).length;
      }
      
      // Check if this is a section header (numbered sections)
      if (line.match(/^\d+\.\s+/)) {
        // Flush paragraph buffer before starting new section
        if (paragraphBuffer.length > 0) {
          htmlContent += `<p>${paragraphBuffer.join(' ')}</p>\n`;
          paragraphBuffer = [];
        }
        
        if (inSection) {
          htmlContent += `</section>`;
        }
        const sectionTitle = line.trim();
        htmlContent += `<section class="mb-8">\n<h2>${sectionTitle}</h2>\n`; // Add section wrapper
        inSection = true;
        currentSection = sectionTitle;
      } else if (line.trim() !== '') {
        // Add to paragraph buffer
        paragraphBuffer.push(line.trim());
      } else if (line.trim() === '') {
        // Empty line means paragraph break
        if (paragraphBuffer.length > 0) {
          htmlContent += `<p>${paragraphBuffer.join(' ')}</p>\n`;
          paragraphBuffer = [];
        }
      }
    }
    
    // Flush any remaining paragraph content
    if (paragraphBuffer.length > 0) {
      htmlContent += `<p>${paragraphBuffer.join(' ')}</p>\n`;
    }
    
    if (inSection) {
      htmlContent += `</section>`;
    }
    
    // Calculate read time (average reading speed: 225 words per minute)
    const readTimeMinutes = Math.max(1, Math.ceil(wordCount / 225));
    const readTimeText = `${readTimeMinutes} min read`;
    
    // Calculate prev/next links
    const prevStory = index > 0 ? storyFilesInfo[index - 1] : null;
    const nextStory = index < storyFilesInfo.length - 1 ? storyFilesInfo[index + 1] : null;
    
    const metadata = {
      prevNext: {
        prev: prevStory ? { title: prevStory.title, url: prevStory.url } : null,
        next: nextStory ? { title: nextStory.title, url: nextStory.url } : null
      },
      storyMetadata: {
        author: story.author,
        date: story.date,
        readTime: readTimeText
      }
    };
    
    // Create HTML file
    const htmlFilePath = path.join('./stories', `${story.htmlFileName}.html`);
    fs.writeFileSync(htmlFilePath, createStoryHtmlTemplate(story.title, htmlContent, metadata));
    
    console.log(`Generated story: ${htmlFilePath}`);
  } catch (error) {
    console.error(`Error processing ${story.originalFile}:`, error.message);
  }
});

// Generate stories index.html
function generateStoriesIndex() {
  console.log('Generating stories/index.html...');
  
  try {
    // Generate HTML for story cards
    const storyCardsHTML = storyFilesInfo.map(story => {
      // Calculate word count for read time
      const filePath = path.join(contentDir, story.originalFile);
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');
      
      let wordCount = 0;
      let skipLines = 1; // Skip title line
      
      // Skip metadata lines
      for (let i = 1; i < Math.min(10, lines.length); i++) {
        const line = lines[i].trim();
        if (line.startsWith('Author:') || line.startsWith('Date:') || line.trim() === '') {
          skipLines++;
        } else {
          break;
        }
      }
      
      // Count words
      for (let i = skipLines; i < lines.length; i++) {
        if (lines[i].trim() !== '' && !lines[i].match(/^\d+\.\s+/)) {
          wordCount += lines[i].trim().split(/\s+/).length;
        }
      }
      
      const readTimeMinutes = Math.max(1, Math.ceil(wordCount / 225));
      const readTimeText = `${readTimeMinutes} min read`;
      
      return `
            <article class="border border-gray-800 p-6 rounded-lg hover:border-gray-700 transition-colors">
                <h2 class="text-xl md:text-2xl mb-3">
                    <a href="${story.htmlFileName}.html" class="hover:text-accent transition-colors">${story.title}</a>
                </h2>
                <p class="text-gray-300 mb-4">${story.excerpt}</p>
                <div class="text-sm text-gray-500">
                    ${story.author ? `<span class="mr-3">By ${story.author}</span>` : ''}
                    ${story.date ? `<time class="mr-3" datetime="${story.date}">${story.date}</time>` : ''}
                    <span>${readTimeText}</span>
                </div>
            </article>`;
    }).join('\n');
    
    // Create stories index.html
    const storiesIndexHTML = `<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Short Stories | Sex & Love Letters</title>
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexandloveletters.com/stories/index.html">
    <meta property="og:title" content="Short Stories | Sex & Love Letters">
    <meta property="og:description" content="A collection of short stories exploring the depths of human connection, culture, and relationships.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada.png">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/stories/index.html">
    <meta property="twitter:title" content="Short Stories | Sex & Love Letters">
    <meta property="twitter:description" content="A collection of short stories exploring the depths of human connection, culture, and relationships.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#ff0000',
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .font-serif {
                font-family: Georgia, Cambria, "Times New Roman", Times, serif;
            }
        }
    </style>
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-16">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-20 md:w-24 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-8">Short Stories</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow">
        <nav class="flex justify-center gap-6 md:gap-12 text-lg mb-12">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <a href="#" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </a>
                <div class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden group-hover:block z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="mb-8 text-center">
            <a href="../collection.html" class="text-accent hover:underline text-sm">← Back to Collection</a>
        </div>

        <section class="mb-10 text-center max-w-2xl mx-auto">
            <p class="text-lg text-gray-300">Welcome to our collection of short stories. Each narrative explores the complex interplay of relationships, culture, and human connection.</p>
        </section>

        <section class="grid gap-12 mb-16">
            ${storyCardsHTML}
            
            <!-- More stories will be added here -->
        </section>
    </main>

    <footer class="w-full max-w-3xl text-center py-8 text-xs text-gray-500">
        <p>&copy; 2025 • <a href="mailto:<EMAIL>" class="hover:text-accent transition-colors">Jonathan Opperman</a> • <a href="https://mastodon.social/@jonathanopperman" class="hover:text-accent transition-colors" target="_blank" rel="me noopener">
            <svg class="inline-block w-4 h-4 ml-1 align-text-bottom" fill="currentColor" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                <path d="M41.0662,20.4015c0,6.2578-.9723,8.3975-2.8838,10.19C34.3081,34.2257,24,33.9433,24,33.9433a40.2923,40.2923,0,0,1-7.0141-.5285S15.86,40.829,30.09,37.1931l-.1445,4.2408c-2.1685.29-22.807,7.92-22.956-18.657l-.056-2.3759c0-6.2578.05-8.4823,2.8838-11.611C13.3831,4.8527,24,5.3547,24,5.3547s10.617-.502,14.182,3.4353c2.8336,3.129,2.8838,5.3535,2.8838,11.611" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M24,23.3125V17.3351a4.6542,4.6542,0,0,0-4.6543-4.6542h0a4.6542,4.6542,0,0,0-4.6542,4.6542v9.6463" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M24,17.3351a4.6542,4.6542,0,0,1,4.6543-4.6542h0a4.6542,4.6542,0,0,1,4.6542,4.6542v9.6463" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </a> • All Rights Reserved</p>
    </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            document.addEventListener("click", function closeDropdown(e) {
                const subscribeButton = document.getElementById("subscribeButton");
                if (e.target !== subscribeButton </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !subscribeButton.contains(e.target) </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !dropdown.contains(e.target)) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            });
        }
    </script>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            document.addEventListener("click", function closeDropdown(e) {
                const subscribeButton = document.getElementById("subscribeButton");
                if (e.target !== subscribeButton </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            document.addEventListener("click", function closeDropdown(e) {
                const subscribeButton = document.getElementById("subscribeButton");
                if (e.target !== subscribeButton </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !subscribeButton.contains(e.target) </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !dropdown.contains(e.target)) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            });
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            document.addEventListener("click", function closeDropdown(e) {
                const subscribeButton = document.getElementById("subscribeButton");
                if (e.target !== subscribeButton </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !subscribeButton.contains(e.target) </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !dropdown.contains(e.target)) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            });
        }
    </script> !subscribeButton.contains(e.target) </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            document.addEventListener("click", function closeDropdown(e) {
                const subscribeButton = document.getElementById("subscribeButton");
                if (e.target !== subscribeButton </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !subscribeButton.contains(e.target) </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !dropdown.contains(e.target)) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            });
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>

    <script>
        // Function to toggle the dropdown menu
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            document.addEventListener("click", function closeDropdown(e) {
                const subscribeButton = document.getElementById("subscribeButton");
                if (e.target !== subscribeButton </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !subscribeButton.contains(e.target) </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script></footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script> !dropdown.contains(e.target)) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            });
        }
    </script> !dropdown.contains(e.target)) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            });
        }
    </script>
</body>
</html>`;
    
    // Write the file
    fs.writeFileSync('./stories/index.html', storiesIndexHTML);
    console.log('Generated stories/index.html');
  } catch (error) {
    console.error('Error generating stories index:', error.message);
  }
}

// Call the function to generate stories index
generateStoriesIndex();

console.log('Stories generation complete!');
