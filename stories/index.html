<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Short Stories | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexandloveletters.com/stories/index.html">
    <meta property="og:title" content="Short Stories | Sex & Love Letters">
    <meta property="og:description" content="A collection of short stories exploring the depths of human connection, culture, and relationships.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/stories/index.html">
    <meta property="twitter:title" content="Short Stories | Sex & Love Letters">
    <meta property="twitter:description" content="A collection of short stories exploring the depths of human connection, culture, and relationships.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <link rel="stylesheet" href="../assets/styles.css">
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-16">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-20 md:w-24 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-8">Short Stories</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow">
        <nav class="flex justify-center gap-6 md:gap-12 text-lg mb-12">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="hover:text-accent transition-colors">Collection</a>
            <a href="index.html" class="text-accent hover:underline">Stories</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
        </nav>

        <section class="mb-10 text-center max-w-2xl mx-auto">
            <p class="text-lg text-gray-300">Welcome to our collection of short stories. Each narrative explores the complex interplay of relationships, culture, and human connection.</p>
        </section>

        <section class="grid gap-12 mb-16">
            
            <article class="border border-gray-800 p-6 rounded-lg hover:border-gray-700 transition-colors">
                <h2 class="text-xl md:text-2xl mb-3">
                    <a href="umqombothi.html" class="hover:text-accent transition-colors">The Secret to Mama Cebisa's Best Umqombothi</a>
                </h2>
                <p class="text-gray-300 mb-4">1. The Pot Mama Cebisa stirred the umqombothi as if it were something living. The air in the courtyard was thick with the scent of malt and maize, sti...</p>
                <div class="text-sm text-gray-500">
                    <span class="mr-3">By Jonathan Opperman</span>
                    <time class="mr-3" datetime="June 10, 2025">June 10, 2025</time>
                    <span>7 min read</span>
                </div>
            </article>
            
            <!-- More stories will be added here -->
        </section>
    </main>

    <footer class="w-full max-w-3xl py-8 text-center text-gray-500 text-sm">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") </footer></footer> !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>