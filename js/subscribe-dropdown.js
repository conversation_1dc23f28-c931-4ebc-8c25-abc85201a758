// Function to toggle the RSS subscription dropdown
function toggleDropdown(event) {
    event.preventDefault();
    const dropdown = document.getElementById("subscribeDropdown");
    dropdown.classList.toggle("hidden");
    
    // Close dropdown when clicking outside
    const closeDropdown = (e) => {
        if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
            dropdown.classList.add("hidden");
            document.removeEventListener("click", closeDropdown);
        }
    };
    document.addEventListener("click", closeDropdown);
} 