<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Test - Sex & Love Letters</title>
    
    <!-- Current favicon setup with cache busting -->
    <link rel="icon" href="assets/favicon.svg?v=1750050583" type="image/svg+xml">
    <link rel="icon" href="assets/favicon.png?v=1750050583" type="image/png">
    <link rel="shortcut icon" href="assets/favicon.ico?v=1750050583">
    <link rel="apple-touch-icon" href="assets/favicon.png?v=1750050583">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .favicon-preview img {
            display: block;
            margin: 0 auto 5px;
            border: 1px solid #ddd;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>Favicon Test Page</h1>
    <p>This page helps you test if the favicon is loading correctly.</p>
    
    <div class="test-section">
        <h2>Current Favicon Status</h2>
        <p>Check the browser tab - you should see the cicada favicon.</p>
        <p class="info">Timestamp: 1750050583</p>
    </div>
    
    <div class="test-section">
        <h2>Favicon Files Preview</h2>
        <div class="favicon-preview">
            <img src="assets/favicon.svg?v=1750050583" width="32" height="32" alt="SVG Favicon">
            <small>SVG (32px)</small>
        </div>
        <div class="favicon-preview">
            <img src="assets/favicon.png?v=1750050583" width="32" height="32" alt="PNG Favicon">
            <small>PNG (32px)</small>
        </div>
        <div class="favicon-preview">
            <img src="assets/favicon.ico?v=1750050583" width="32" height="32" alt="ICO Favicon">
            <small>ICO (32px)</small>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Direct Links</h2>
        <ul>
            <li><a href="assets/favicon.svg?v=1750050583" target="_blank">SVG Favicon</a></li>
            <li><a href="assets/favicon.png?v=1750050583" target="_blank">PNG Favicon</a></li>
            <li><a href="assets/favicon.ico?v=1750050583" target="_blank">ICO Favicon</a></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Troubleshooting Steps</h2>
        <ol>
            <li><strong>Hard Refresh:</strong> Press Ctrl+F5 (or Cmd+Shift+R on Mac)</li>
            <li><strong>Clear Browser Cache:</strong> Go to browser settings and clear cache</li>
            <li><strong>Incognito Mode:</strong> Open this page in a private/incognito window</li>
            <li><strong>Different Browser:</strong> Try opening in a different browser</li>
            <li><strong>Check Network Tab:</strong> Open Developer Tools → Network tab and reload to see if favicon loads</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>For Cloudflare Users</h2>
        <p>If you're using Cloudflare, you may need to:</p>
        <ul>
            <li>Go to Cloudflare Dashboard → Caching → Purge Cache</li>
            <li>Select "Custom purge" and add these URLs:
                <ul>
                    <li>https://sexandloveletters.com/assets/favicon.svg</li>
                    <li>https://sexandloveletters.com/assets/favicon.png</li>
                    <li>https://sexandloveletters.com/assets/favicon.ico</li>
                </ul>
            </li>
        </ul>
    </div>
    
    <p><a href="index.html">← Back to main site</a></p>
    
    <script>
        // Check if favicons are loading
        window.addEventListener('load', function() {
            const links = document.querySelectorAll('link[rel*="icon"]');
            console.log('Favicon links found:', links.length);
            links.forEach((link, index) => {
                console.log(`Favicon ${index + 1}:`, link.href);
            });
        });
    </script>
</body>
</html>
