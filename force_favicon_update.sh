#!/bin/bash
# <PERSON><PERSON>t to force favicon update by adding timestamp-based cache busting

echo "Forcing favicon update with timestamp cache busting..."

# Get current timestamp
TIMESTAMP=$(date +%s)

echo "Using timestamp: $TIMESTAMP"

# Update all HTML files to use timestamp-based cache busting
find . -name "*.html" -not -path "./node_modules/*" -exec sed -i "s/favicon\.\(svg\|png\|ico\)?v=[0-9]*/favicon.\1?v=$TIMESTAMP/g" {} \;

# Also update the generate.js template
sed -i "s/?v=[0-9]*/?v=$TIMESTAMP/g" generate.js

echo "Updated all favicon references with timestamp: $TIMESTAMP"
echo "Now regenerating content..."

# Regenerate all content
./regenerate_all.sh

echo "Favicon update complete!"
echo ""
echo "Additional steps you can take:"
echo "1. Clear your browser cache completely (Ctrl+Shift+Delete)"
echo "2. Try opening the site in an incognito/private window"
echo "3. Check the favicon directly: https://sexandloveletters.com/assets/favicon.svg?v=$TIMESTAMP"
echo "4. If using Cloudflare, purge cache for favicon files specifically"
