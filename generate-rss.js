// Generate RSS feeds for Sex & Love Letters website
const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');
const RSS = require('rss');

console.log('Starting RSS generation...');

// Current date in RFC 822 format (required for RSS)
const currentDate = new Date().toUTCString();
const websiteURL = 'https://sexandloveletters.com';

console.log('Current date:', currentDate);

// Helper function to generate an RFC 822 date from a string date
function formatRFC822Date(dateStr) {
  if (!dateStr) return currentDate;
  
  try {
    return new Date(dateStr).toUTCString();
  } catch (e) {
    return currentDate;
  }
}

// Create feeds directory if it doesn't exist
const feedsDir = path.join(__dirname, 'feeds');
if (!fs.existsSync(feedsDir)) {
    fs.mkdirSync(feedsDir);
}

// Function to read content from markdown files
function readContent(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const { data, content: markdownContent } = matter(content);
    return {
        title: data.title || path.basename(filePath, '.md'),
        date: data.date || new Date(),
        content: markdownContent
    };
}

// Function to clean content for RSS
function cleanContent(content) {
    // Remove markdown formatting
    return content
        .replace(/^#+\s+/gm, '') // Remove headers
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
        .replace(/\*(.*?)\*/g, '$1') // Remove italic
        .replace(/\[(.*?)\]\((.*?)\)/g, '$1') // Remove links
        .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
        .replace(/\n{3,}/g, '\n\n') // Normalize multiple line breaks
        .trim();
}

// Function to determine content type based on filename
function getContentType(filename) {
    const lowerFilename = filename.toLowerCase();
    if (lowerFilename === 'little gecko.md' || lowerFilename === 'mei-mei.md') {
        return 'letter';
    } else if (lowerFilename === 'umqombothi.md') {
        return 'story';
    }
    return 'poem';
}

// Create and write the main RSS feed
const mainFeed = new RSS({
    title: 'Sex and Love Letters',
    description: 'A collection of poems, letters, and stories about love, sex, and everything in between',
    feed_url: 'https://sexandloveletters.com/feeds/main.xml',
    site_url: 'https://sexandloveletters.com',
    language: 'en',
    pubDate: new Date(),
    custom_namespaces: {
        'content': 'http://purl.org/rss/1.0/modules/content/'
    }
});

// Create separate feeds for each content type
const poemFeed = new RSS({
    title: 'Sex and Love Letters - Poems',
    description: 'Poems about love, sex, and everything in between',
    feed_url: 'https://sexandloveletters.com/feeds/poems.xml',
    site_url: 'https://sexandloveletters.com/poems',
    language: 'en',
    pubDate: new Date(),
    custom_namespaces: {
        'content': 'http://purl.org/rss/1.0/modules/content/'
    }
});

const letterFeed = new RSS({
    title: 'Sex and Love Letters - Letters',
    description: 'Letters about love, sex, and everything in between',
    feed_url: 'https://sexandloveletters.com/feeds/letters.xml',
    site_url: 'https://sexandloveletters.com/letters',
    language: 'en',
    pubDate: new Date(),
    custom_namespaces: {
        'content': 'http://purl.org/rss/1.0/modules/content/'
    }
});

const storyFeed = new RSS({
    title: 'Sex and Love Letters - Stories',
    description: 'Stories about love, sex, and everything in between',
    feed_url: 'https://sexandloveletters.com/feeds/stories.xml',
    site_url: 'https://sexandloveletters.com/stories',
    language: 'en',
    pubDate: new Date(),
    custom_namespaces: {
        'content': 'http://purl.org/rss/1.0/modules/content/'
    }
});

// Read all content files
const contentDir = path.join(__dirname, 'content');
const files = fs.readdirSync(contentDir)
    .filter(file => file.endsWith('.md'))
    .sort((a, b) => {
        const aDate = fs.statSync(path.join(contentDir, a)).mtime;
        const bDate = fs.statSync(path.join(contentDir, b)).mtime;
        return bDate - aDate;
    });

// Add items to feeds
files.forEach(file => {
    const { title, date, content } = readContent(path.join(contentDir, file));
    const contentType = getContentType(file);
    const fileName = path.basename(file, '.md');
    
    // Convert filename to URL-friendly format (lowercase, replace spaces with hyphens, etc.)
    const urlSafeFileName = fileName
        .toLowerCase()
        .replace(/\s+/g, '-')           // Replace spaces with hyphens
        .replace(/[()]/g, '')           // Remove parentheses
        .replace(/['?.]/g, '')          // Remove apostrophes, question marks, periods
        .replace(/&/g, 'and');          // Replace & with 'and'
    
    // Fix the plural for 'story' -> 'stories'
    const folderName = contentType === 'story' ? 'stories' : `${contentType}s`;
    const url = `https://sexandloveletters.com/${folderName}/${urlSafeFileName}.html`;
    const cleanText = cleanContent(content);
    
    const item = {
        title,
        description: cleanText,
        url,
        guid: url,
        date,
        custom_elements: [
            {'content:encoded': cleanText},
            {'content:type': contentType}
        ]
    };

    mainFeed.item(item);
    
    switch (contentType) {
        case 'poem':
            poemFeed.item(item);
            break;
        case 'letter':
            letterFeed.item(item);
            break;
        case 'story':
            storyFeed.item(item);
            break;
    }
});

// Write feeds to files
fs.writeFileSync(path.join(feedsDir, 'main.xml'), mainFeed.xml({ indent: true }));
fs.writeFileSync(path.join(feedsDir, 'poems.xml'), poemFeed.xml({ indent: true }));
fs.writeFileSync(path.join(feedsDir, 'letters.xml'), letterFeed.xml({ indent: true }));
fs.writeFileSync(path.join(feedsDir, 'stories.xml'), storyFeed.xml({ indent: true }));

console.log('RSS feeds generated successfully!');
