<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Little Gecko | Sex & Love Letters</title>
    <!-- Favicon with cache busting -->
    <link rel="icon" href="../assets/favicon.svg?v=1750052079" type="image/svg+xml">
    <link rel="icon" href="../assets/favicon.png?v=1750052079" type="image/png">
    <link rel="shortcut icon" href="../assets/favicon.ico?v=1750052079">
    <link rel="apple-touch-icon" href="../assets/favicon.png?v=1750052079">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://sexandloveletters.com/letters/little-gecko.html">
    <meta property="og:title" content="Little Gecko | Sex & Love Letters">
    <meta property="og:description" content="A love letter exploring intimacy, desire, and vulnerability from Sex & Love Letters.">
    <meta property="og:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="1200">
    <meta property="og:image:alt" content="Cicada Logo - Sex & Love Letters">
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sexandloveletters.com/letters/little-gecko.html">
    <meta property="twitter:title" content="Little Gecko | Sex & Love Letters">
    <meta property="twitter:description" content="A love letter exploring intimacy, desire, and vulnerability from Sex & Love Letters.">
    <meta property="twitter:image" content="https://sexandloveletters.com/assets/cicada-social.png">
    <!-- RSS Feeds -->
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - All Content" href="https://sexandloveletters.com/feeds/main.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Poems" href="https://sexandloveletters.com/feeds/poems.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Love Letters" href="https://sexandloveletters.com/feeds/letters.xml">
    <link rel="alternate" type="application/rss+xml" title="Sex & Love Letters - Stories" href="https://sexandloveletters.com/feeds/stories.xml">
    <link rel="stylesheet" href="../assets/styles.css">
</head>
<body class="bg-black text-white font-serif min-h-screen flex flex-col items-center px-4">
    <header class="w-full max-w-3xl flex flex-col items-center py-12 md:py-24">
        <a href="../index.html">
            <img src="../assets/cicada.png" alt="Cicada Logo" class="w-24 md:w-32 mb-6">
        </a>
        <h1 class="text-2xl md:text-3xl font-normal tracking-wider mb-2">Sex & Love Letters</h1>
    </header>

    <main class="w-full max-w-3xl flex-grow flex flex-col items-center">
        
        <nav class="flex gap-6 md:gap-12 text-lg mb-16">
            <a href="../index.html" class="hover:text-accent transition-colors">Home</a>
            <a href="../collection.html" class="text-accent hover:underline">Collection</a>
            <a href="../about.html" class="hover:text-accent transition-colors">About</a>
            <div class="relative group">
                <button id="subscribeButton" onclick="toggleDropdown(event)" class="hover:text-accent transition-colors flex items-center" aria-haspopup="true">
                    <img src="../assets/rss-icon.svg" alt="RSS Feeds" class="w-5 h-5 mr-1">
                    <span>Subscribe</span>
                </button>
                <div id="subscribeDropdown" class="absolute left-0 mt-2 w-48 bg-black border border-gray-700 rounded shadow-lg hidden z-10">
                    <a href="../feeds/main.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">All Content</a>
                    <a href="../feeds/poems.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Poems</a>
                    <a href="../feeds/letters.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Letters</a>
                    <a href="../feeds/stories.xml" class="block px-4 py-2 text-sm hover:text-accent transition-colors">Stories</a>
                </div>
            </div>
        </nav>

        <div class="w-full max-w-lg">
            <h2 class="text-xl font-normal mb-8 text-center">Little Gecko</h2>

            <div class="letter-content my-8">
                <p>Little Gecko,</p><p>Falling in love with you wasn’t something I did willingly. But I won’t lie about it. These feelings are mine. I live with emotional honesty, even when it wrecks me. We only fool ourselves by pretending otherwise, and fighting it just ends up hurting in other ways, sometimes worse than the hurt we tried to dodge.</p><p>We got caught in a whirlwind. A raging, beautiful storm. And holy noodle, what a romance it was. Despite how much I cared, I didn’t expect too much from you. The talk of cinema reel flash-forwards was sweet, even exciting, but I had no illusions about things getting serious too fast. As tempting as that was. Still, I hoped it would be inevitable. We all chase happiness. It’s what we do. Maybe that’s why we always seemed to drift into the deep end of the pool when we talked.</p><p>That’s on me, too. Wearing your heart on your sleeve means you bleed a little easier. So if I rushed us, I’m sorry. I didn’t know a better way. I just followed my gut. And something in me thought we had something rare.</p><p>Romance and love \- they’re science and art. I’ve never mastered either. Maybe I never will. Maybe I’ll die trying. Life’s too short for regret, guilt, or pain... and taxes, but we both know those are unavoidable. So we pay what we owe. We keep going. And in between, we get the good stuff. The music, the art, the wine, the weed, the laughter. Skinny dipping. Kisses. Friends. Sex. The stars. And if we’re lucky, love. If we’re brave enough to work at it. I know it’s scary. But don’t write it off. When it’s not poison, it’s medicine. It heals more than it harms.</p><p>It’s hard to be light with a heavy heart, but I’m trying. Sometimes being stubborn is what saves me. I hope you’re okay.</p><p>I miss you.</p><p>J</p>
            </div>

            <div class="mt-8 flex justify-center gap-4">
                <a href="https://twitter.com/intent/tweet?text=Little%20Gecko&url=https://sexandloveletters.com/letters/little-gecko.html" 
                   class="text-gray-400 hover:text-accent transition-colors" 
                   target="_blank" rel="noopener noreferrer">
                   Share on Twitter
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=https://sexandloveletters.com/letters/little-gecko.html" 
                   class="text-gray-400 hover:text-accent transition-colors"
                   target="_blank" rel="noopener noreferrer">
                   Share on Facebook
                </a>
            </div>

            <div class="mt-12 border-t border-gray-800 pt-6 flex justify-between">
                <span></span>
                <a href="../collection.html" class="text-accent hover:underline">Back to Collection</a>
                <a href="mei-mei.html" class="text-accent hover:underline">Mei-mei →</a>
            </div>
        </div>
    </main>

    
    <footer class="w-full max-w-3xl py-12 text-center text-gray-400">
        <p>&copy; 2025 Sex & Love Letters. All rights reserved.</p>
    </footer>

    <script src="/js/subscribe-dropdown.js"></script>
    <script>
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById("subscribeDropdown");
            dropdown.classList.toggle("hidden");
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!e.target.closest("#subscribeButton") && !e.target.closest("#subscribeDropdown")) {
                    dropdown.classList.add("hidden");
                    document.removeEventListener("click", closeDropdown);
                }
            };
            document.addEventListener("click", closeDropdown);
        }
    </script>
</body>
</html>